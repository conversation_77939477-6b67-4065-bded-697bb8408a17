<?php

namespace App\Services\Company;

use App\Repositories\AreaRepository;
use App\Repositories\CompanyAdminRepository;
use App\Repositories\CompanyImmediateShippingCityRepository;
use App\Repositories\CompanyRepository;
use App\Repositories\CompanyShippingTypeRepository;
use App\Repositories\CompanyShippingTypeSizeRepository;
use App\Repositories\RoleRepository;
use App\Services\MediaService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class RegistrationService
{
    public function __construct(
        private readonly CompanyRepository $companyRepository,
        private readonly CompanyAdminRepository $companyAdminRepository,
        private readonly MediaService $mediaService,
        private readonly CompanyShippingTypeRepository $companyShippingTypeRepository,
        private readonly CompanyShippingTypeSizeRepository $companyShippingTypeSizeRepository,
        private readonly CompanyImmediateShippingCityRepository $companyImmediateShippingCityRepository,
        private readonly AreaRepository $areaRepository,
        private readonly RoleRepository $roleRepository
    ) {}

    public function register(): void
    {
        $data = request()->all();

        $this->validateRegistration();

        DB::transaction(function () use ($data): void {

            $data['code'] = $this->generateCode();
            $data['phone'] = normalizeMobileNumber($data['phone']);
            $data['approval_status'] = 'pending';

            $company = $this->companyRepository->register($data);

            if (request('commercial_registration_certificate')) {
                $this->mediaService->save($company, request('commercial_registration_certificate'), 'companies', 'commercial_registration_certificate');
            }

            if (request('cargo_insurance_certificate')) {
                $this->mediaService->save($company, request('cargo_insurance_certificate'), 'companies', 'cargo_insurance_certificate');
            }

            if (request('tax_certificate')) {
                $this->mediaService->save($company, request('tax_certificate'), 'companies', 'tax_certificate');
            }

            if (request('logo')) {
                $this->mediaService->save($company, request('logo'), 'companies', 'logo');
            }

            foreach (request('shipping_options') as $shipping_option) {
                $companyShippingType = $this->companyShippingTypeRepository->create(
                    $company,
                    $shipping_option['shipping_type_id'],
                    $shipping_option['shipping_type_id'] == 1 ? null : ($shipping_option['has_express_delivery'] ?? 0)
                );

                foreach ($shipping_option['shipping_sizes'] as $shipping_size_id) {
                    $companyShippingTypeSize = $this->companyShippingTypeSizeRepository->create($companyShippingType, $shipping_size_id);

                    // Check if transportation methods are provided for this size
                    if (isset($shipping_option['size_methods'][$shipping_size_id])) {
                        foreach ($shipping_option['size_methods'][$shipping_size_id] as $transportion_method_id) {
                            $companyShippingTypeSize->transportionMethods()->create([
                                'company_shipping_type_id' => $companyShippingType->id,
                                'transportion_method_id' => $transportion_method_id,
                            ]);
                        }
                    }
                }

                // Immediate shipping
                if ($shipping_option['shipping_type_id'] == 1) {
                    foreach ($shipping_option['cities'] as $city) {
                        $companyImmediateShippingCity = $this->companyImmediateShippingCityRepository->create($company, $city['id']);
                        $companyImmediateShippingCity->areas()->attach($city['areas']);
                    }
                }

                // Intercity shipping
                if ($shipping_option['shipping_type_id'] == 2) {
                    $company->intercityShippingPickupCities()->attach($shipping_option['pickup_cities'], ['type' => 'pickup']);
                    $company->intercityShippingDeliveryCities()->attach($shipping_option['delivery_cities'], ['type' => 'delivery']);
                }

                // International shipping
                if ($shipping_option['shipping_type_id'] == 3) {
                    $company->internationalShippingCountires()->attach($shipping_option['shipping_countries']);
                }
            }

            // Creating Super Admin
            $data['company_id'] = $company->id;
            $data['admin_phone'] = normalizeMobileNumber($data['admin_phone']);
            $data['admin_password'] = bcrypt($data['admin_password']);
            $data['admin_role_id'] = 3;
            $data['is_super_admin'] = 1;

            $admin = $this->companyAdminRepository->register($data);
            $superAdminRole = $this->roleRepository->getCompanySuperAdmin();
            $admin->permissions()->attach($superAdminRole->permissions);
        });
    }

    public function validateRegistration(): void
    {
        foreach (request('shipping_options') as $shipping_option) {

            // Immediate shipping
            if ($shipping_option['shipping_type_id'] == 1) {
                foreach ($shipping_option['cities'] as $city) {
                    foreach ($city['areas'] as $area) {
                        $area = $this->areaRepository->getById($area);

                        if ($area->city_id != $city['id']) {
                            throw new BadRequestHttpException("area id $area->id doesn't belong to city id $city[id]");
                        }
                    }
                }
            }
        }
    }

    public function generateCode(): string
    {
        do {
            $code = strtoupper(Str::random(10));
        } while ($this->companyRepository->getByCode($code));

        return $code;
    }
}
