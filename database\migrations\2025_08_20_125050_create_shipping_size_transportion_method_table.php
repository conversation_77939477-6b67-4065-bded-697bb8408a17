<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipping_size_transportion_method', function (Blueprint $table) {
            $table->id();
            $table->foreignId('shipping_size_id')
                ->constrained('shipping_sizes')
                ->onDelete('cascade');
            $table->foreignId('transportion_method_id')
                ->constrained('transportion_methods')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipping_size_transportion_method');
    }
};
