<?php

namespace Database\Seeders;

use App\Models\ShippingSize;
use App\Models\TransportionMethod;
use Illuminate\Database\Seeder;

class ShippingSizeTransportionMethodSeeder extends Seeder
{
    public function run(): void
    {
        // Define relationships between shipping sizes and transportation methods
        $relationships = [
            'small' => ['bike', 'motorcycle'],           // Small packages can use bike or motorcycle
            'large' => ['motorcycle', 'lury'],           // Large packages need motorcycle or lury
            'furniture' => ['lury'],                     // Furniture only uses lury (truck)
            'refrigerated' => ['lury'],                  // Refrigerated items only use lury (refrigerated truck)
        ];

        foreach ($relationships as $sizeName => $methodNames) {
            $shippingSize = ShippingSize::where('name', $sizeName)->first();

            if ($shippingSize) {
                $transportationMethodIds = TransportionMethod::whereIn('name', $methodNames)
                    ->pluck('id')
                    ->toArray();

                $shippingSize->transportationMethods()->sync($transportationMethodIds);
            }
        }
    }
}
