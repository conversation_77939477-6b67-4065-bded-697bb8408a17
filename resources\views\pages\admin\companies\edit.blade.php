<x-layout :title="__('Edit Company')">
    <x-session-message />
    <form action="{{ route('admin.companies.update', $company->id) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')
        <div class="card">
            <div class="card-body">

                <!-- BASIC INFO -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3">{{ __('Basic Info') }}</h5>
                    <div class="row">
                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Company Name') }}</b></label>
                            <input type="text" name="name" class="form-control"
                                value="{{ old('name', $company->name) }}">
                            <x-input-error name="name" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Business Registration Number') }}</b></label>
                            <input type="text" name="business_registration_number" class="form-control"
                                value="{{ old('business_registration_number', $company->business_registration_number) }}">
                            <x-input-error name="business_registration_number" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Email') }}</b></label>
                            <input type="text" name="email" class="form-control"
                                value="{{ old('email', $company->email) }}" autocomplete="off">
                            <x-input-error name="email" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Phone') }}</b></label>
                            <x-phone-input :countries="$countries" :country_code="old('country_code', $company->country_code)" :phone="old('phone', $company->phone)" />
                            <x-input-error name="phone" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Address') }}</b></label>
                            <input type="text" name="address" class="form-control"
                                value="{{ old('address', $company->address) }}">
                            <x-input-error name="address" />
                        </div>

                        <!-- Status -->
                        <div class="mb-3 col-lg-4">
                            <label style="margin-bottom:15px" class="form-label"><b>{{ __('Status') }}</b></label><br>
                            <label class="switch">
                                <input type="checkbox" class="switch-input" name="status" @checked(old('status', $company->status) == 'active')>
                                <span class="switch-toggle-slider">
                                    <span class="switch-on"></span>
                                    <span class="switch-off"></span>
                                </span>
                                <span class="switch-label">{{ __('active') }}</span>
                            </label>
                            <x-input-error name="status" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Logo') }}</b></label>
                            <input type="file" name="logo" class="form-control">
                            <x-input-error name="logo" />
                        </div>

                        <!-- Current Logo -->
                        @if ($company->logo)
                            <div class="mb-4 col-lg-4">
                                <label class="form-label"><b>{{ __('Current Logo') }}</b></label>
                                <div class="mt-2">
                                    <a href="{{ $company->logo->url }}" target="_blank">
                                        <img src="{{ $company->logo->url }}" alt="Current Logo" class="card-image">
                                    </a>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- BANK ACCOUNT INFO -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3">{{ __('Bank Account Info') }}</h5>
                    <div class="row">
                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Bank Name') }}</b></label>
                            <input type="text" name="bank_name" class="form-control"
                                value="{{ old('bank_name', $company->bank_name) }}">
                            <x-input-error name="bank_name" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Bank Account Owner') }}</b></label>
                            <input type="text" name="bank_account_owner" class="form-control"
                                value="{{ old('bank_account_owner', $company->bank_account_owner) }}">
                            <x-input-error name="bank_account_owner" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Bank Account Number') }}</b></label>
                            <input type="text" name="bank_account_number" class="form-control"
                                value="{{ old('bank_account_number', $company->bank_account_number) }}">
                            <x-input-error name="bank_account_number" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('IBAN') }}</b></label>
                            <input type="text" name="iban" class="form-control"
                                value="{{ old('iban', $company->iban) }}">
                            <x-input-error name="iban" />
                        </div>
                    </div>
                </div>

                <!-- ADMIN INFO -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3">{{ __('Admin Info') }}</h5>
                    <div class="row">
                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Admin Name') }}</b></label>
                            <input type="text" name="admin_name" class="form-control"
                                value="{{ old('admin_name', $company->superAdmin->name) }}">
                            <x-input-error name="admin_name" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Admin Email') }}</b></label>
                            <input type="text" name="admin_email" class="form-control"
                                value="{{ old('admin_email', $company->superAdmin->email) }}">
                            <x-input-error name="admin_email" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Admin Phone') }}</b></label>
                            <x-phone-input :countries="$countries" :country_code="old('admin_country_code', $company->superAdmin->country_code)" :phone="old('admin_phone', $company->superAdmin->phone)" prefix="admin_" />
                            <x-input-error name="admin_phone" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Password') }}</b></label>
                            <input type="password" name="admin_password" class="form-control"
                                autocomplete="new-password">
                            <x-input-error name="admin_password" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Confirm Password') }}</b></label>
                            <input type="password" name="admin_password_confirmation" class="form-control">
                        </div>
                    </div>
                </div>

                <!-- DOCUMENTS -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3">{{ __('Certificates') }}</h5>
                    <div class="row">

                        <!-- Commercial Registration Certificate -->
                        <div class="mb-4 col-lg-4">
                            <label><b>{{ __('Commercial Registration Certificate') }}</b></label>
                            <input type="file" name="commercial_registration_certificate" class="form-control">
                            <x-input-error name="commercial_registration_certificate" />

                            @if ($company->commercialRegistrationCertificate)
                                <label
                                    class="form-label mt-4"><b>{{ __('Current Commercial Registration Certificate') }}</b></label>
                                <div class="mt-1">
                                    <a href="{{ $company->commercialRegistrationCertificate->url }}" target="_blank">
                                        <img src="{{ $company->commercialRegistrationCertificate->url }}"
                                            alt="Commercial Registration Certificate" class="card-image">
                                    </a>
                                </div>
                            @endif
                        </div>

                        <!-- Cargo Insurance Certificate -->
                        <div class="mb-4 col-lg-4">
                            <label><b>{{ __('Cargo Insurance Certificate') }}</b></label>
                            <input type="file" name="cargo_insurance_certificate" class="form-control">
                            <x-input-error name="cargo_insurance_certificate" />

                            @if ($company->cargoInsuranceCertificate)
                                <label
                                    class="form-label mt-4"><b>{{ __('Current Cargo Insurance Certificate') }}</b></label>
                                <div class="mt-1">
                                    <a href="{{ $company->cargoInsuranceCertificate->url }}" target="_blank">
                                        <img src="{{ $company->cargoInsuranceCertificate->url }}"
                                            alt="Cargo Insurance Certificate" class="card-image">
                                    </a>
                                </div>
                            @endif
                        </div>

                        <!-- Tax Certificate -->
                        <div class="mb-4 col-lg-4">
                            <label><b>{{ __('Tax Certificate') }}</b></label>
                            <input type="file" name="tax_certificate" class="form-control">
                            <x-input-error name="tax_certificate" />

                            @if ($company->taxCertificate)
                                <label class="form-label mt-4"><b>{{ __('Current Tax Certificate') }}</b></label>
                                <div class="mt-1">
                                    <a href="{{ $company->taxCertificate->url }}" target="_blank">
                                        <img src="{{ $company->taxCertificate->url }}" alt="Tax Certificate"
                                            class="card-image">
                                    </a>
                                </div>
                            @endif
                        </div>

                    </div>
                </div>

                <!-- SHIPPING OPTIONS -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3">{{ __('Shipping Options') }}</h5>
                    <div id="shipping-options-list">
                        @foreach ($shippingTypes as $index => $type)
                            @php
                                $companyShippingType = $company->shippingTypes
                                    ->where('shipping_type_id', $type->id)
                                    ->first();
                                $isEnabled = (bool) $companyShippingType;
                            @endphp
                            <div class="border rounded p-3 mb-3">
                                <input type="hidden" name="shipping_options[{{ $index }}][shipping_type_id]"
                                    value="{{ $type->id }}">

                                <div class="form-check mb-2">
                                    <input class="form-check-input shipping-type-toggle" type="checkbox"
                                        name="shipping_options[{{ $index }}][enabled]" value="1"
                                        id="shipping_type_{{ $type->id }}" {{ $isEnabled ? 'checked' : '' }}
                                        data-target="#shipping-type-details-{{ $type->id }}">
                                    <label class="form-check-label fw-bold" for="shipping_type_{{ $type->id }}">
                                        {{ $type->name }}
                                    </label>
                                </div>

                                <div class="shipping-type-details ps-3"
                                    id="shipping-type-details-{{ $type->id }}"
                                    style="{{ $isEnabled ? '' : 'display: none;' }}">
                                    @if ($type->id != 1)
                                        <!-- Express Delivery -->
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                    name="shipping_options[{{ $index }}][has_express_delivery]"
                                                    value="1" id="express_{{ $type->id }}"
                                                    {{ $companyShippingType && $companyShippingType->has_express_delivery ? 'checked' : '' }}>
                                                <label class="form-check-label" for="express_{{ $type->id }}">
                                                    {{ __('Has Express Delivery') }}
                                                </label>
                                            </div>
                                        </div>
                                    @endif

                                    <!-- Sizes with Transportation Methods -->
                                    <div class="mb-3">
                                        <label class="fw-bold">{{ __('Sizes & Transportation Methods') }}</label>
                                        @foreach ($shippingSizes as $sizeIndex => $size)
                                            @php
                                                $companySize = $companyShippingType
                                                    ? $companyShippingType->sizes
                                                        ->where('shipping_size_id', $size->id)
                                                        ->first()
                                                    : null;
                                                $isSizeSelected = (bool) $companySize;
                                                $selectedMethods = $companySize
                                                    ? $companySize->transportionMethods
                                                        ->pluck('transportion_method_id')
                                                        ->toArray()
                                                    : [];
                                            @endphp
                                            <div class="border rounded p-3 mb-3 shipping-size-container"
                                                data-shipping-type-index="{{ $index }}"
                                                data-size-id="{{ $size->id }}">

                                                <!-- Size Checkbox -->
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input shipping-size-checkbox"
                                                        type="checkbox"
                                                        name="shipping_options[{{ $index }}][shipping_sizes][]"
                                                        value="{{ $size->id }}"
                                                        id="size_{{ $type->id }}_{{ $size->id }}"
                                                        {{ $isSizeSelected ? 'checked' : '' }}
                                                        data-target="#size-methods-{{ $type->id }}-{{ $size->id }}">
                                                    <label class="form-check-label fw-bold"
                                                        for="size_{{ $type->id }}_{{ $size->id }}">
                                                        {{ $size->name }}
                                                    </label>
                                                </div>

                                                <!-- Transportation Methods for this Size -->
                                                <div class="ps-3 size-transportation-methods"
                                                    id="size-methods-{{ $type->id }}-{{ $size->id }}"
                                                    style="{{ $isSizeSelected ? '' : 'display: none;' }}">
                                                    <label
                                                        class="fw-bold small text-muted">{{ __('Transportation Methods') }}</label>
                                                    <div class="row transportation-methods-container"
                                                        data-shipping-type-index="{{ $index }}"
                                                        data-size-id="{{ $size->id }}">
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                        <x-input-error :name="'shipping_options.' . $index . '.shipping_sizes'" />
                                    </div>

                                    <!-- LOCATIONS -->
                                    @if ($type->id === 1)
                                        <!-- Cities & Areas -->
                                        <div class="mb-3">
                                            <label class="fw-bold">{{ __('Cities & Areas') }}</label>
                                            <div class="shipping-city-area-wrapper" data-index="{{ $index }}">
                                                @foreach ($company->immediateShippingCities as $cityIndex => $companyCity)
                                                    <div class="border rounded p-2 mb-2 city-area-group">
                                                        <div class="mb-2">
                                                            <label class="fw-bold">City</label>
                                                            <select class="form-select city-select"
                                                                name="shipping_options[{{ $index }}][cities][{{ $cityIndex }}][id]"
                                                                data-shipping-index="{{ $index }}"
                                                                data-city-index="{{ $cityIndex }}"
                                                                id="city_select_{{ $index }}_{{ $cityIndex }}">
                                                                <option value="">Select City</option>
                                                                @foreach ($cities as $city)
                                                                    <option value="{{ $city->id }}"
                                                                        {{ $city->id == $companyCity->city_id ? 'selected' : '' }}>
                                                                        {{ $city->name }}</option>
                                                                @endforeach
                                                            </select>
                                                        </div>
                                                        <div class="area-checkboxes mt-2"
                                                            id="area_box_{{ $index }}_{{ $cityIndex }}">
                                                            @foreach ($companyCity->city->areas as $area)
                                                                <div class="form-check">
                                                                    <input class="form-check-input" type="checkbox"
                                                                        name="shipping_options[{{ $index }}][cities][{{ $cityIndex }}][areas][]"
                                                                        value="{{ $area->id }}"
                                                                        id="area_{{ $index }}_{{ $cityIndex }}_{{ $area->id }}"
                                                                        {{ $companyCity->areas->contains($area->id) ? 'checked' : '' }}>
                                                                    <label class="form-check-label"
                                                                        for="area_{{ $index }}_{{ $cityIndex }}_{{ $area->id }}">
                                                                        {{ $area->name }}
                                                                    </label>
                                                                </div>
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                @endforeach
                                            </div>

                                            <button type="button"
                                                class="btn btn-sm btn-outline-primary mt-2 add-city-button"
                                                data-index="{{ $index }}">
                                                {{ __('Add City') }}
                                            </button>
                                        </div>
                                    @endif

                                    @if ($type->id === 2)
                                        <!-- Pickup Cities -->
                                        <div class="mb-3">
                                            <label class="fw-bold">{{ __('Pickup Cities') }}</label>
                                            <select class="form-select select2"
                                                name="shipping_options[{{ $index }}][pickup_cities][]"
                                                multiple data-placeholder="{{ __('Choose') }}...">
                                                @foreach ($cities as $city)
                                                    <option value="{{ $city->id }}"
                                                        {{ $company->intercityShippingPickupCities->contains($city->id) ? 'selected' : '' }}>
                                                        {{ $city->name }}</option>
                                                @endforeach
                                            </select>
                                            <x-input-error :name="'shipping_options.' . $index . '.pickup_cities'" />
                                        </div>

                                        <!-- Delivery Cities -->
                                        <div class="mb-3">
                                            <label class="fw-bold">{{ __('Delivery Cities') }}</label>
                                            <select class="form-select select2"
                                                name="shipping_options[{{ $index }}][delivery_cities][]"
                                                multiple data-placeholder="{{ __('Choose') }}...">
                                                @foreach ($cities as $city)
                                                    <option value="{{ $city->id }}"
                                                        {{ $company->intercityShippingDeliveryCities->contains($city->id) ? 'selected' : '' }}>
                                                        {{ $city->name }}</option>
                                                @endforeach
                                            </select>
                                            <x-input-error :name="'shipping_options.' . $index . '.delivery_cities'" />
                                        </div>
                                    @endif

                                    @if ($type->id === 3)
                                        <!-- Countries -->
                                        <div class="mb-3">
                                            <label class="fw-bold">{{ __('Countries') }}</label>
                                            <select class="form-select select2"
                                                name="shipping_options[{{ $index }}][shipping_countries][]"
                                                multiple data-placeholder="{{ __('Choose') }}...">
                                                @foreach ($countries as $country)
                                                    @if ($country->id != 1)
                                                        <option value="{{ $country->id }}"
                                                            {{ $company->internationalShippingCountires->contains($country->id) ? 'selected' : '' }}>
                                                            {{ $country->name }}</option>
                                                    @endif
                                                @endforeach
                                            </select>
                                            <x-input-error :name="'shipping_options.' . $index . '.shipping_countries'" />
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <button type="submit" class="btn btn-outline-primary">{{ __('Update') }}</button>

            </div>
        </div>
    </form>

    @push('js')
        <script>
            // Company existing data for edit form
            window.companyShippingData = {
                @foreach ($company->shippingTypes as $companyShippingType)
                    {{ $loop->index }}: {
                        shipping_type_id: {{ $companyShippingType->shipping_type_id }},
                        size_methods: {
                            @foreach ($companyShippingType->sizes as $companySize)
                                {{ $companySize->shipping_size_id }}: [
                                    @foreach ($companySize->transportionMethods as $method)
                                        '{{ $method->transportion_method_id }}'
                                        {{ !$loop->last ? ',' : '' }}
                                    @endforeach
                                ] {{ !$loop->last ? ',' : '' }}
                            @endforeach
                        }
                    }
                    {{ !$loop->last ? ',' : '' }}
                @endforeach
            };

            document.addEventListener('DOMContentLoaded', function() {
                const form = document.querySelector('form');

                // Initialize toggle for all shipping-type checkboxes
                document.querySelectorAll('.shipping-type-toggle').forEach(function(checkbox) {
                    toggleShippingDetails(checkbox); // Set initial visibility
                    checkbox.addEventListener('change', function() {
                        toggleShippingDetails(this);
                    });
                });

                function toggleShippingDetails(checkbox) {
                    const targetId = checkbox.dataset.target;
                    const details = document.querySelector(targetId);
                    if (!details) return;

                    if (checkbox.checked) {
                        details.style.display = 'block';
                    } else {
                        details.style.display = 'none';
                        // Uncheck all inputs inside when hiding
                        const inputs = details.querySelectorAll('input, select');
                        inputs.forEach(input => {
                            if (input.type === 'checkbox' || input.type === 'radio') {
                                input.checked = false;
                            } else {
                                input.value = '';
                            }
                        });
                    }
                }

                // Function to get existing selected methods for a size (for edit form)
                function getExistingMethodsForSize(shippingTypeIndex, sizeId) {
                    // Get existing data from the global company shipping data
                    if (window.companyShippingData && window.companyShippingData[shippingTypeIndex]) {
                        return window.companyShippingData[shippingTypeIndex].size_methods[sizeId] || [];
                    }
                    return [];
                }

                // Handle showing/hiding transportation methods for individual shipping sizes
                function updateTransportationMethodsForSize(shippingTypeIndex, sizeId, isChecked) {
                    console.log('updateTransportationMethodsForSize called:', shippingTypeIndex, sizeId, isChecked);

                    // Find the shipping type ID from the hidden input
                    const shippingTypeContainer = document.querySelector(
                        `input[name="shipping_options[${shippingTypeIndex}][shipping_type_id]"]`);
                    const shippingTypeId = shippingTypeContainer ? shippingTypeContainer.value : shippingTypeIndex;

                    console.log('Looking for container with ID:', `size-methods-${shippingTypeId}-${sizeId}`);

                    const methodsContainer = document.querySelector(
                        `#size-methods-${shippingTypeId}-${sizeId}`
                    );

                    if (!methodsContainer) {
                        console.log('Methods container not found for ID:', `size-methods-${shippingTypeId}-${sizeId}`);
                        return;
                    }

                    if (isChecked) {
                        // Show the transportation methods container
                        methodsContainer.style.display = 'block';

                        const transportationContainer = methodsContainer.querySelector(
                            '.transportation-methods-container'
                        );

                        // Show loading spinner
                        transportationContainer.innerHTML = `
                            <div class="d-flex align-items-center text-primary">
                                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                <span>Loading transportation methods...</span>
                            </div>
                        `;

                        // Fetch available transportation methods for this specific size
                        fetch(`/api/transportion-methods-by-shipping-size?shipping_size_id=${sizeId}`, {
                                method: 'GET',
                                headers: {
                                    'Accept': 'application/json',
                                }
                            })
                            .then(response => {
                                console.log('Response status:', response.status);
                                console.log('Response headers:', response.headers);
                                if (!response.ok) {
                                    throw new Error(`HTTP error! status: ${response.status}`);
                                }
                                return response.json();
                            })
                            .then(data => {
                                console.log('API response for size', sizeId, ':', data);

                                if (data.data) {
                                    const availableMethods = data.data;
                                    console.log('Available methods for size', sizeId, ':', availableMethods);

                                    // Clear loading spinner
                                    transportationContainer.innerHTML = '';

                                    // Get existing selected methods for this size (for edit form)
                                    const existingMethods = getExistingMethodsForSize(shippingTypeIndex, sizeId);

                                    // Create transportation method checkboxes
                                    availableMethods.forEach(method => {
                                        const colDiv = document.createElement('div');
                                        colDiv.classList.add('col-md-3', 'mb-2');

                                        const formCheck = document.createElement('div');
                                        formCheck.classList.add('form-check');

                                        const checkbox = document.createElement('input');
                                        checkbox.classList.add('form-check-input');
                                        checkbox.type = 'checkbox';
                                        checkbox.value = method.id;
                                        checkbox.id = `method_${shippingTypeId}_${sizeId}_${method.id}`;
                                        checkbox.name =
                                            `shipping_options[${shippingTypeIndex}][size_methods][${sizeId}][]`;

                                        // Check if method was previously selected
                                        if (existingMethods.includes(method.id.toString())) {
                                            checkbox.checked = true;
                                        }

                                        const label = document.createElement('label');
                                        label.classList.add('form-check-label');
                                        label.htmlFor = checkbox.id;
                                        label.textContent = method.name;

                                        formCheck.appendChild(checkbox);
                                        formCheck.appendChild(label);
                                        colDiv.appendChild(formCheck);
                                        transportationContainer.appendChild(colDiv);
                                    });

                                    if (availableMethods.length === 0) {
                                        transportationContainer.innerHTML =
                                            '<div class="text-muted">No transportation methods available for this size</div>';
                                    }
                                } else {
                                    console.log('API response structure:', data);
                                    transportationContainer.innerHTML =
                                        '<div class="text-danger">Failed to load transportation methods</div>';
                                }
                            })
                            .catch(error => {
                                console.error('Error fetching transportation methods for size', sizeId, ':', error);
                                transportationContainer.innerHTML = `
                                    <div class="text-danger">
                                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                        Error loading transportation methods. Please try again.
                                    </div>
                                `;
                            });

                    } else {
                        // Hide the transportation methods container and uncheck all methods
                        methodsContainer.style.display = 'none';
                        const allCheckboxes = methodsContainer.querySelectorAll('input[type="checkbox"]');
                        allCheckboxes.forEach(cb => {
                            cb.checked = false;
                        });
                    }
                }

                // Function to initialize transportation methods for checked shipping sizes
                function initializeCheckedShippingSizes() {
                    const checkedSizes = document.querySelectorAll('.shipping-size-checkbox:checked');
                    console.log('Initializing transportation methods for', checkedSizes.length, 'checked sizes');

                    checkedSizes.forEach((checkbox, index) => {
                        const nameMatch = checkbox.name.match(/shipping_options\[(\d+)\]/);
                        const sizeId = checkbox.value;
                        const shippingTypeIndex = nameMatch ? nameMatch[1] : null;

                        if (shippingTypeIndex && sizeId) {
                            // Stagger the API calls to avoid overwhelming the server
                            setTimeout(() => {
                                console.log('Initializing size:', sizeId, 'for type index:',
                                    shippingTypeIndex);
                                updateTransportationMethodsForSize(shippingTypeIndex, sizeId, true);
                            }, index * 200); // 200ms delay between each call
                        }
                    });
                }

                // Add event listeners for shipping size changes
                const shippingSizeCheckboxes = document.querySelectorAll('.shipping-size-checkbox');
                console.log('Found shipping size checkboxes:', shippingSizeCheckboxes.length);

                shippingSizeCheckboxes.forEach(checkbox => {
                    console.log('Adding listener to:', checkbox.name, 'Checked:', checkbox.checked);

                    checkbox.addEventListener('change', function() {
                        console.log('Shipping size checkbox changed:', this.name, this.checked);

                        // Extract shipping type index and size ID
                        const nameMatch = this.name.match(/shipping_options\[(\d+)\]/);
                        const sizeId = this.value;
                        const shippingTypeIndex = nameMatch ? nameMatch[1] : null;

                        if (shippingTypeIndex && sizeId) {
                            console.log('Extracted shipping type index:', shippingTypeIndex, 'Size ID:',
                                sizeId);
                            updateTransportationMethodsForSize(shippingTypeIndex, sizeId, this.checked);
                        }
                    });
                });

                // Initialize transportation methods for already checked shipping sizes
                setTimeout(() => {
                    initializeCheckedShippingSizes();
                }, 500); // Wait 500ms for DOM to be fully ready

                // Safety net for form submit: disable all unchecked shipping type inputs
                form.addEventListener('submit', function() {
                    document.querySelectorAll('.shipping-type-toggle').forEach(function(checkbox) {
                        if (!checkbox.checked) {
                            const targetId = checkbox.dataset.target;
                            const details = document.querySelector(targetId);
                            if (!details) return;

                            const inputs = details.querySelectorAll('input, select, textarea');
                            inputs.forEach(input => input.disabled = true);
                        }
                    });
                });

            });
        </script>
    @endpush

</x-layout>
