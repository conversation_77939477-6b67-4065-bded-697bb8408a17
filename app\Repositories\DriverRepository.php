<?php

namespace App\Repositories;

use App\Enum\OrderStatus;
use App\Models\Driver;
use App\Models\Order;
use Illuminate\Support\Facades\DB;
use MatanYadaev\EloquentSpatial\Objects\Point;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class DriverRepository
{
    public function __construct(private readonly Driver $model) {}

    public function getById($id)
    {
        return $this->model->find($id);
    }

    public function getByPhone($country_code, $phone)
    {
        return $this->model->where('country_code', $country_code)->where('phone', $phone)->first();
    }

    public function register(array $data)
    {
        return $this->model->create([
            'name' => $data['name'],
            'country_code' => $data['country_code'],
            'phone' => $data['phone'],
            'email' => $data['email'] ?? null,
            'company_id' => $data['company_id'],
            'gender' => $data['gender'] ?? null,
            'birth_date' => $data['birth_date'] ?? null,
            'id_number' => $data['id_number'] ?? null,
            'bank_name' => $data['bank_name'],
            'bank_account_owner' => $data['bank_account_owner'],
            'bank_account_number' => $data['bank_account_number'],
            'iban' => $data['iban'] ?? null,
        ]);
    }

    public function create(array $data)
    {
        return $this->model->create([
            'name' => $data['name'],
            'country_code' => $data['country_code'],
            'phone' => $data['phone'],
            'email' => $data['email'] ?? null,
            'company_id' => $data['company_id'],
            'gender' => $data['gender'] ?? null,
            'birth_date' => $data['birth_date'] ?? null,
            'id_number' => $data['id_number'] ?? null,
            'bank_name' => $data['bank_name'],
            'bank_account_owner' => $data['bank_account_owner'],
            'bank_account_number' => $data['bank_account_number'],
            'iban' => $data['iban'] ?? null,
            'approval_status' => $data['approval_status'],
        ]);
    }

    public function getNearbyDrivers(Order $order, Point $location, float $radiusInMeters = 5000)
    {
        return $this->model
            ->approved()
            ->active()
            ->available()
            ->whereRelation('shippingTypes', 'shipping_type_id', $order->shipping_type_id)
            ->whereDoesntHave('orders', fn ($query) => $query->active())
            ->whereHas('activeVehicle')
            ->whereHas(
                'company',
                fn ($query) => $query->active()
                    ->whereHas('shippingTypes', function ($query) use ($order) {
                        $query->where('shipping_type_id', $order->shipping_type_id);

                        if ($order->shipping_type_id != 1 && $order->is_express) {
                            $query->where('has_express_delivery', true);
                        }
                    })
            )
            ->selectRaw('drivers.*, ST_Distance_Sphere(POINT(current_lng, current_lat), POINT(?, ?)) as distance', [
                $location->longitude,
                $location->latitude,
            ])
            ->whereRaw('ST_Distance_Sphere(POINT(current_lng, current_lat), POINT(?, ?)) <= ?', [
                $location->longitude,
                $location->latitude,
                $radiusInMeters,
            ])
            ->orderBy('distance')
            ->get();
    }

    public function update(Driver $driver, array $data): void
    {
        $driver->update($data);
    }

    public function updateLocation(Driver $driver, float $lat, float $lng): void
    {
        $driver->update([
            'current_lat' => $lat,
            'current_lng' => $lng,
        ]);
    }

    public function findById($id)
    {
        return $this->model->find($id);
    }

    public function invalidateUniqueData(Driver $driver): void
    {
        $driver->update([
            'phone' => getInvalidatedValue($driver->phone),
            'email' => getInvalidatedValue($driver->email),
            'id_number' => getInvalidatedValue($driver->id_number),
        ]);
    }

    public function hasActiveOrder(Driver $driver)
    {
        return $driver->orders()->whereIn('status', [
            OrderStatus::READY_TO_PICKUP->value,
            OrderStatus::HEADING_TO_PICKUP->value,
            OrderStatus::ARRIVED_AT_PICKUP->value,
            OrderStatus::PICKED_UP->value,
            OrderStatus::IN_TRANSIT->value,
            OrderStatus::ARRIVED_AT_DELIVERY_LOCATION->value,
        ])->exists();
    }

    public function delete(Driver $driver)
    {
        $hasActiveOrder = $this->hasActiveOrder($driver);

        if ($hasActiveOrder) {
            throw new BadRequestHttpException(__('Driver has an active order.'));
        }

        DB::transaction(function () use ($driver): void {
            $driver->delete();
            $this->invalidateUniqueData($driver);
        });
    }
}
