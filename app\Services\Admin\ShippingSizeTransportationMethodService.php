<?php

namespace App\Services\Admin;

use App\Repositories\ShippingSizeRepository;
use Illuminate\Support\Facades\DB;

class ShippingSizeTransportationMethodService
{
    public function __construct(
        private ShippingSizeRepository $shippingSizeRepository
    ) {}

    /**
     * Update transportation methods for all shipping sizes.
     *
     * @param  array<int, array<int>>  $assignments
     */
    public function updateAssignments(array $assignments): void
    {
        DB::transaction(function () use ($assignments) {
            foreach ($assignments as $shippingSizeId => $methods) {
                $shippingSize = $this->shippingSizeRepository->findById($shippingSizeId);
                $shippingSize->transportationMethods()->sync($methods);
            }
        });
    }
}
