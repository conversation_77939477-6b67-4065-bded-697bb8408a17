<?php

namespace Tests\Feature;

use App\Models\ShippingSize;
use App\Models\TransportionMethod;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TransportionMethodsByShippingSizeTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->createTestData();
    }

    private function createTestData()
    {
        // Create transportation methods
        $bike = TransportionMethod::create(['name' => 'bike', 'status' => 'active']);
        $motorcycle = TransportionMethod::create(['name' => 'motorcycle', 'status' => 'active']);
        $lury = TransportionMethod::create(['name' => 'lury', 'status' => 'active']);
        $inactive = TransportionMethod::create(['name' => 'inactive', 'status' => 'inactive']);

        // Create shipping sizes
        $small = ShippingSize::create(['name' => 'small']);
        $large = ShippingSize::create(['name' => 'large']);
        $furniture = ShippingSize::create(['name' => 'furniture']);

        // Associate transportation methods with shipping sizes
        $small->transportationMethods()->attach([$bike->id, $motorcycle->id]);
        $large->transportationMethods()->attach([$motorcycle->id, $lury->id]);
        $furniture->transportationMethods()->attach([$lury->id]);

        // Associate inactive method with small size to test filtering
        $small->transportationMethods()->attach([$inactive->id]);
    }

    public function test_can_get_transportation_methods_for_single_shipping_size()
    {
        $small = ShippingSize::where('name', 'small')->first();

        $response = $this->getJson("/api/transportion-methods-by-shipping-size?shipping_size_id={$small->id}");

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                '*' => [
                    'id',
                    'name',
                ],
            ],
        ]);

        $data = $response->json('data');
        $this->assertCount(2, $data); // Should only return active methods (bike, motorcycle)

        $methodNames = collect($data)->pluck('name')->toArray();
        $this->assertContains('bike', $methodNames);
        $this->assertContains('motorcycle', $methodNames);
        $this->assertNotContains('inactive', $methodNames);
    }

    public function test_can_get_transportation_methods_for_large_shipping_size()
    {
        $large = ShippingSize::where('name', 'large')->first();

        $response = $this->getJson("/api/transportion-methods-by-shipping-size?shipping_size_id={$large->id}");

        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(2, $data); // Should return motorcycle, lury

        $methodNames = collect($data)->pluck('name')->toArray();
        $this->assertContains('motorcycle', $methodNames);
        $this->assertContains('lury', $methodNames);
    }

    public function test_can_get_transportation_methods_for_furniture_shipping_size()
    {
        $furniture = ShippingSize::where('name', 'furniture')->first();

        $response = $this->getJson("/api/transportion-methods-by-shipping-size?shipping_size_id={$furniture->id}");

        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(1, $data); // Should return only lury

        $methodNames = collect($data)->pluck('name')->toArray();
        $this->assertContains('lury', $methodNames);
    }

    public function test_validation_fails_when_shipping_size_id_is_missing()
    {
        $response = $this->getJson('/api/transportion-methods-by-shipping-size');

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['shipping_size_id']);
    }

    public function test_validation_fails_when_shipping_size_id_does_not_exist()
    {
        $response = $this->getJson('/api/transportion-methods-by-shipping-size?shipping_size_id=999');

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['shipping_size_id']);
    }

    public function test_returns_empty_array_when_no_transportation_methods_associated()
    {
        $emptySize = ShippingSize::create(['name' => 'empty']);

        $response = $this->getJson("/api/transportion-methods-by-shipping-size?shipping_size_id={$emptySize->id}");

        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(0, $data);
    }
}
