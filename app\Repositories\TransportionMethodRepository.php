<?php

namespace App\Repositories;

use App\Models\TransportionMethod;

class TransportionMethodRepository
{
    public function __construct(private readonly TransportionMethod $model) {}

    public function getById(string $id)
    {
        return $this->model->find($id);
    }

    public function getByIds(array $ids)
    {
        return $this->model->whereIn('id', $ids)->get();
    }

    public function getActiveMethods()
    {
        return $this->model->where('status', 'active')->get();
    }

    public function getByShippingSizes(array $shippingSizeIds)
    {
        // Get transportation methods that are associated with the selected shipping sizes from pivot table
        return $this->model
            ->where('status', 'active')
            ->whereHas('shippingSizes', function ($query) use ($shippingSizeIds) {
                $query->whereIn('shipping_sizes.id', $shippingSizeIds);
            })
            ->distinct()
            ->get();
    }
}
