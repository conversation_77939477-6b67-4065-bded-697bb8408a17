@foreach ($shippingSizes as $shippingSize)
    <div class="card mb-3">
        <div class="card-header">
            <strong>{{ $shippingSize->name }}</strong>
        </div>
        <div class="card-body">
            <label class="d-block mb-2">Transportation Methods</label>
            <div class="row">
                @foreach ($transportationMethods as $transportationMethod)
                    @php
                        $inputId = "method_{$shippingSize->id}_{$transportationMethod->id}";
                        $isChecked = $shippingSize->transportationMethods->contains($transportationMethod->id);
                    @endphp
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox"
                                name="shipping_sizes[{{ $shippingSize->id }}][]" value="{{ $transportationMethod->id }}"
                                id="{{ $inputId }}" {{ $isChecked ? 'checked' : '' }}>
                            <label class="form-check-label" for="{{ $inputId }}">
                                {{ $transportationMethod->name }}
                            </label>
                        </div>
                    </div>
                @endforeach
            </div>

            @error("shipping_sizes.{$shippingSize->id}")
                <div class="text-danger mt-2">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
@endforeach
