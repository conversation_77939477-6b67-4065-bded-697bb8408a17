<?php

namespace App\Models;

use App\Enum\OrderStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Builder;

class Company extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'code',
        'name',
        'country_code',
        'phone',
        'email',
        'address',
        'business_registration_number',
        'bank_name',
        'bank_account_owner',
        'bank_account_number',
        'iban',
        'approval_status',
        'status',
    ];

    public function getCodeAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getNameAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getPhoneAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getEmailAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getBusinessRegistrationNumberAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function scopeActive(Builder $query)
    {
        $query->where('status', 'active');
    }

    public function drivers(): HasMany
    {
        return $this->hasMany(Driver::class);
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    public function logo(): MorphOne
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'logo');
    }

    public function media(): MorphMany
    {
        return $this->morphMany(Media::class, 'mediable');
    }

    public function commercialRegistrationCertificate(): MorphOne
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'commercial_registration_certificate');
    }

    public function taxCertificate(): MorphOne
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'tax_certificate');
    }

    public function cargoInsuranceCertificate(): MorphOne
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'cargo_insurance_certificate');
    }

    public function superAdmin(): HasOne
    {
        return $this->hasOne(CompanyAdmin::class)->where('is_super_admin', true);
    }

    public function admins(): HasMany
    {
        return $this->hasMany(CompanyAdmin::class);
    }

    public function shippingTypes(): HasMany
    {
        return $this->hasMany(CompanyShippingType::class);
    }

    public function internationalShippingCountires(): BelongsToMany
    {
        return $this->belongsToMany(Country::class, 'company_international_shipping_countries', 'company_id', 'country_id');
    }

    public function intercityShippingPickupCities(): BelongsToMany
    {
        return $this->belongsToMany(City::class, 'company_intercity_shipping_cities', 'company_id', 'city_id')->where('type', 'pickup')->where('cities.status', 'active');
    }

    public function intercityShippingDeliveryCities(): BelongsToMany
    {
        return $this->belongsToMany(City::class, 'company_intercity_shipping_cities', 'company_id', 'city_id')->where('type', 'delivery')->where('cities.status', 'active');
    }

    public function immediateShippingCities(): HasMany
    {
        return $this->hasMany(CompanyImmediateShippingCity::class, 'company_id');
    }

    public function internationalShippingCities(): BelongsToMany
    {
        return $this->belongsToMany(City::class, 'company_international_shipping_cities', 'company_id', 'city_id');
    }

    public function orders(): HasManyThrough
    {
        return $this->hasManyThrough(Order::class, Driver::class);
    }

    public function inProgressOrders()
    {
        return $this->orders()->active();
    }
}
