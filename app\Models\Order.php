<?php

namespace App\Models;

use App\Enum\OrderPaymentMethod;
use App\Enum\OrderStatus;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use MatanYadaev\EloquentSpatial\Objects\Point;
use MatanYadaev\EloquentSpatial\Traits\HasSpatial;

class Order extends Model
{
    use HasSpatial, SoftDeletes;

    const MAX_SEARCH_ATTEMPTS = 5; // minutes

    const SEARCH_RADIUS_STEP = 5000; // in meters

    protected $fillable = [
        'user_id',
        'driver_id',
        'shipping_type_id',
        'shipping_size_id',
        'transportion_method_id',
        'pickup_country_id',
        'pickup_address_id',
        'pickup_location',
        'description',
        'payment_method',
        'status',
        'cancel_reason',
        'is_express',
        'fees',
        'cost',
        'total',
        'driver_search_attempts',
        'driver_search_started_at',
        'last_driver_search_at',
        'driver_search_completed',
        'vehicle_id',
        'order_number',
        'cancel_reason',
    ];

    protected $casts = [
        'pickup_location' => Point::class,
        'payment_method' => OrderPaymentMethod::class,
        'status' => OrderStatus::class,
        'is_express' => 'boolean',
    ];

    public function scopeActive(Builder $query): void
    {
        $query->whereIn('orders.status', [
            OrderStatus::READY_TO_PICKUP->value,
            OrderStatus::HEADING_TO_PICKUP->value,
            OrderStatus::ARRIVED_AT_PICKUP->value,
            OrderStatus::PICKED_UP->value,
            OrderStatus::IN_TRANSIT->value,
            OrderStatus::ARRIVED_AT_DELIVERY_LOCATION->value,
        ]);
    }

    public function scopeForDriver(Builder $query): void
    {
        $query->where('driver_id', auth('driver')->id());
    }

    public function isFast(): bool
    {
        return $this->shipping_type_id === 1 || ($this->is_express && in_array($this->shipping_type_id, [2, 3]));
    }

    public function getPickupCoordinatesAttribute(): array
    {
        $location = $this->pickup_location ?? $this->pickupAddress->location ?? null;

        return [
            'lat' => $location?->latitude ?? null,
            'lng' => $location?->longitude ?? null,
        ];
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function driver(): BelongsTo
    {
        return $this->belongsTo(Driver::class);
    }

    public function shippingType(): BelongsTo
    {
        return $this->belongsTo(ShippingType::class);
    }

    public function shippingSize(): BelongsTo
    {
        return $this->belongsTo(ShippingSize::class);
    }

    public function transportionMethod(): BelongsTo
    {
        return $this->belongsTo(TransportionMethod::class);
    }

    public function pickupAddress(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'pickup_address_id')->withTrashed();
    }

    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    public function driverActions(): HasMany
    {
        return $this->hasMany(OrderDriverAction::class);
    }

    public function chat(): MorphOne
    {
        return $this->morphOne(Chat::class, 'chatable');
    }

    public function getIsOneShipmentOrderAttribute(): bool
    {
        if ($this->shipping_type_id == 1) {
            return true;
        }

        return in_array($this->shipping_size_id, [2, 3]) && $this->is_express;
    }

    public function vehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class);
    }

    public function pickupCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'pickup_country_id');
    }

    public function pdf(): MorphOne
    {
        return $this->morphOne(Media::class, 'mediable')->where('type', 'pdf');
    }
}
