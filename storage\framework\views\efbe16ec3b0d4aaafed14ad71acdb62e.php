<?php $__currentLoopData = $shippingSizes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $shippingSize): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <div class="card mb-3">
        <div class="card-header">
            <strong><?php echo e($shippingSize->name); ?></strong>
        </div>
        <div class="card-body">
            <label class="d-block mb-2">Transportation Methods</label>
            <div class="row">
                <?php $__currentLoopData = $transportationMethods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transportationMethod): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php
                        $inputId = "method_{$shippingSize->id}_{$transportationMethod->id}";
                        $isChecked = $shippingSize->transportationMethods->contains($transportationMethod->id);
                    ?>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox"
                                name="shipping_sizes[<?php echo e($shippingSize->id); ?>][]" value="<?php echo e($transportationMethod->id); ?>"
                                id="<?php echo e($inputId); ?>" <?php echo e($isChecked ? 'checked' : ''); ?>>
                            <label class="form-check-label" for="<?php echo e($inputId); ?>">
                                <?php echo e($transportationMethod->name); ?>

                            </label>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <?php $__errorArgs = ["shipping_sizes.{$shippingSize->id}"];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                <div class="text-danger mt-2">
                    <?php echo e($message); ?>

                </div>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
        </div>
    </div>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH D:\laravel\packz\resources\views/pages/admin/shipping_size_transportion_method/form.blade.php ENDPATH**/ ?>