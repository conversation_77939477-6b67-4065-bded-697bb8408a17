<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ShippingSizeTransportionMethod\StoreRequest;
use App\Repositories\ShippingSizeRepository;
use App\Repositories\TransportionMethodRepository;
use App\Services\Admin\ShippingSizeTransportationMethodService;

class ShippingSizeTransportionMethodController extends Controller
{
    public function __construct(
        private readonly ShippingSizeRepository $shippingSizeRepository,
        private readonly TransportionMethodRepository $transportionMethodRepository,
        private readonly ShippingSizeTransportationMethodService $shippingSizeTransportationMethodService

    ) {
        //
    }

    public function create()
    {
        $shippingSizes = $this->shippingSizeRepository->getAll();
        $transportationMethods = $this->transportionMethodRepository->getActiveMethods();

        return view('pages.admin.shipping_size_transportion_method.create', compact('shippingSizes', 'transportationMethods'));
    }

    public function store(StoreRequest $request)
    {
        $this->shippingSizeTransportationMethodService->updateAssignments($request->validated('shipping_sizes'));

        return redirect()->back()->with('success', __('Transportation methods updated for all shipping sizes successfully.'));
    }
}
