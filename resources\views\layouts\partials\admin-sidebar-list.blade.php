<!-- Home -->
<li class="menu-item">
    <a href="{{ route('admin.index') }}" class="menu-link">
        <i class="menu-icon tf-icons ti ti-home"></i>
        <div data-i18n="Home">{{ __('Home') }}</div>
    </a>
</li>

<!-- Admins -->
@if (auth('admin')->user()->hasPermission('show admins'))
    <li class="menu-item">
        <a href="{{ route('admin.admins.index') }}" class="menu-link">
            <i class="menu-icon tf-icons ti ti-user-bolt"></i>
            <div data-i18n="Admins">{{ __('Admins') }}</div>
        </a>
    </li>
@endif

<!-- Users -->
@if (auth('admin')->user()->hasPermission('show users'))
    <li class="menu-item">
        <a href="{{ route('admin.users.index') }}" class="menu-link">
            <i class="menu-icon tf-icons ti ti-user"></i>
            <div data-i18n="Users">{{ __('Users') }}</div>
        </a>
    </li>
@endif

<!-- Drivers -->
@if (auth('admin')->user()->hasPermission('show drivers'))
    <li class="menu-item">
        <a href="{{ route('admin.drivers.index') }}" class="menu-link">
            <i class="menu-icon tf-icons ti ti-user-pause"></i>
            <div data-i18n="Drivers">{{ __('Drivers') }}</div>
        </a>
    </li>
@endif

<!-- Companies -->
@if (auth('admin')->user()->hasPermission('show companies') ||
        auth('admin')->user()->hasPermission('show company registration requests'))
    <li class="menu-item @if (request()->routeIs('admin.companies.*') || request()->routeIs('admin.company-registration-requests.*')) active open @endif">
        <a href="javascript:void(0);" class="menu-link menu-toggle">
            <i class="menu-icon tf-icons ti ti-building"></i>
            <div data-i18n="Companies">{{ __('Companies') }}</div>
        </a>
        <ul class="menu-sub">
            @if (auth('admin')->user()->hasPermission('show companies'))
                <li class="menu-item">
                    <a href="{{ route('admin.companies.index') }}" class="menu-link">
                        <div data-i18n="registered-companies">{{ __('Registered Companies') }}</div>
                    </a>
                </li>
            @endif
            @if (auth('admin')->user()->hasPermission('show company registration requests'))
                <li class="menu-item">
                    <a href="{{ route('admin.company-registration-requests.index') }}" class="menu-link">
                        <div data-i18n="company-registration-requests">{{ __('Company Registration Requests') }}</div>
                    </a>
                </li>
            @endif
        </ul>
    </li>
@endif

<!-- Countries -->
@if (auth('admin')->user()->hasPermission('show countries'))
    <li class="menu-item">
        <a href="{{ route('admin.countries.index') }}" class="menu-link">
            <i class="menu-icon tf-icons ti ti-world"></i>
            <div data-i18n="Countries">{{ __('Countries') }}</div>
        </a>
    </li>
@endif

<!-- Cities -->
@if (auth('admin')->user()->hasPermission('show cities'))
    <li class="menu-item">
        <a href="{{ route('admin.cities.index') }}" class="menu-link">
            <i class="menu-icon tf-icons ti ti-map"></i>
            <div data-i18n="Cities">{{ __('Cities') }}</div>
        </a>
    </li>
@endif

<!-- Areas -->
@if (auth('admin')->user()->hasPermission('show areas'))
    <li class="menu-item">
        <a href="{{ route('admin.areas.index') }}" class="menu-link">
            <i class="menu-icon tf-icons ti ti-map-pin"></i>
            <div data-i18n="Areas">{{ __('Areas') }}</div>
        </a>
    </li>
@endif

<!-- Vehicles -->
@if (auth('admin')->user()->hasPermission('show vehicles'))
    <li class="menu-item">
        <a href="{{ route('admin.vehicles.index') }}" class="menu-link">
            <i class="menu-icon tf-icons ti ti-car"></i>
            <div data-i18n="Vehicles">{{ __('Vehicles') }}</div>
        </a>
    </li>
@endif

<!-- Orders -->
@if (auth('admin')->user()->hasPermission('show orders'))
    <li class="menu-item">
        <a href="{{ route('admin.orders.index') }}" class="menu-link">
            <i class="menu-icon tf-icons ti ti-package"></i>
            <div data-i18n="Orders">{{ __('Orders') }}</div>
        </a>
    </li>
@endif


<li class="menu-item">
    <a href="{{ route('admin.shipping-size-transportion-methods.create') }}" class="menu-link">
        <i class="menu-icon tf-icons ti ti-package"></i>
        <div data-i18n="Shipping Size Transportation Methods">{{ __('Shipping Size Transportation Methods') }}</div>
    </a>
</li>
