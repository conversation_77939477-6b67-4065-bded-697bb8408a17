<?php if (isset($component)) { $__componentOriginal1f9e5f64f242295036c059d9dc1c375c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal1f9e5f64f242295036c059d9dc1c375c = $attributes; } ?>
<?php $component = App\View\Components\Layout::resolve(['title' => __('Register Company')] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Layout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <?php if (isset($component)) { $__componentOriginalf62778ee1ebf045dc63a15e4e0a7647d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf62778ee1ebf045dc63a15e4e0a7647d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.session-message','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('session-message'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf62778ee1ebf045dc63a15e4e0a7647d)): ?>
<?php $attributes = $__attributesOriginalf62778ee1ebf045dc63a15e4e0a7647d; ?>
<?php unset($__attributesOriginalf62778ee1ebf045dc63a15e4e0a7647d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf62778ee1ebf045dc63a15e4e0a7647d)): ?>
<?php $component = $__componentOriginalf62778ee1ebf045dc63a15e4e0a7647d; ?>
<?php unset($__componentOriginalf62778ee1ebf045dc63a15e4e0a7647d); ?>
<?php endif; ?>
    <form action="<?php echo e(route('admin.companies.store')); ?>" method="POST" enctype="multipart/form-data">
        <?php echo csrf_field(); ?>
        <div class="card">
            <div class="card-body">

                <!-- BASIC INFO -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3"><?php echo e(__('Basic Info')); ?></h5>
                    <div class="row">
                        <div class="mb-3 col-lg-4">
                            <label><b><?php echo e(__('Company Name')); ?></b></label>
                            <input type="text" name="name" class="form-control" value="<?php echo e(old('name')); ?>">
                            <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'name']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'name']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b><?php echo e(__('Business Registration Number')); ?></b></label>
                            <input type="text" name="business_registration_number" class="form-control"
                                value="<?php echo e(old('business_registration_number')); ?>">
                            <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'business_registration_number']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'business_registration_number']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b><?php echo e(__('Email')); ?></b></label>
                            <input type="text" name="email" class="form-control" value="<?php echo e(old('email')); ?>"
                                autocomplete="off">
                            <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'email']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'email']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b><?php echo e(__('Phone')); ?></b></label>
                            <?php if (isset($component)) { $__componentOriginal7f129feca299ac4c0aa6a1d3bbb99a8a = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7f129feca299ac4c0aa6a1d3bbb99a8a = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.phone-input','data' => ['countries' => $countries,'countryCode' => old('country_code'),'phone' => old('phone')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('phone-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['countries' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($countries),'country_code' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(old('country_code')),'phone' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(old('phone'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7f129feca299ac4c0aa6a1d3bbb99a8a)): ?>
<?php $attributes = $__attributesOriginal7f129feca299ac4c0aa6a1d3bbb99a8a; ?>
<?php unset($__attributesOriginal7f129feca299ac4c0aa6a1d3bbb99a8a); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7f129feca299ac4c0aa6a1d3bbb99a8a)): ?>
<?php $component = $__componentOriginal7f129feca299ac4c0aa6a1d3bbb99a8a; ?>
<?php unset($__componentOriginal7f129feca299ac4c0aa6a1d3bbb99a8a); ?>
<?php endif; ?>
                            <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'phone']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'phone']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b><?php echo e(__('Address')); ?></b></label>
                            <input type="text" name="address" class="form-control" value="<?php echo e(old('address')); ?>">
                            <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'address']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'address']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b><?php echo e(__('Logo')); ?></b></label>
                            <input type="file" name="logo" class="form-control">
                            <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'logo']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'logo']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- BANK ACCOUNT INFO -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3"><?php echo e(__('Bank Account Info')); ?></h5>
                    <div class="row">
                        <div class="mb-3 col-lg-4">
                            <label><b><?php echo e(__('Bank Name')); ?></b></label>
                            <input type="text" name="bank_name" class="form-control" value="<?php echo e(old('bank_name')); ?>">
                            <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'bank_name']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'bank_name']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b><?php echo e(__('Bank Account Owner')); ?></b></label>
                            <input type="text" name="bank_account_owner" class="form-control"
                                value="<?php echo e(old('bank_account_owner')); ?>">
                            <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'bank_account_owner']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'bank_account_owner']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b><?php echo e(__('Bank Account Number')); ?></b></label>
                            <input type="text" name="bank_account_number" class="form-control"
                                value="<?php echo e(old('bank_account_number')); ?>">
                            <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'bank_account_number']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'bank_account_number']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b><?php echo e(__('IBAN')); ?></b></label>
                            <input type="text" name="iban" class="form-control" value="<?php echo e(old('iban')); ?>">
                            <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'iban']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'iban']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- ADMIN INFO -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3"><?php echo e(__('Admin Info')); ?></h5>
                    <div class="row">
                        <div class="mb-3 col-lg-4">
                            <label><b><?php echo e(__('Admin Name')); ?></b></label>
                            <input type="text" name="admin_name" class="form-control"
                                value="<?php echo e(old('admin_name')); ?>">
                            <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'admin_name']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'admin_name']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b><?php echo e(__('Admin Email')); ?></b></label>
                            <input type="text" name="admin_email" class="form-control"
                                value="<?php echo e(old('admin_email')); ?>">
                            <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'admin_email']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'admin_email']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b><?php echo e(__('Admin Phone')); ?></b></label>
                            <?php if (isset($component)) { $__componentOriginal7f129feca299ac4c0aa6a1d3bbb99a8a = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7f129feca299ac4c0aa6a1d3bbb99a8a = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.phone-input','data' => ['countries' => $countries,'countryCode' => old('admin_country_code'),'phone' => old('admin_phone'),'prefix' => 'admin_']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('phone-input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['countries' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($countries),'country_code' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(old('admin_country_code')),'phone' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(old('admin_phone')),'prefix' => 'admin_']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7f129feca299ac4c0aa6a1d3bbb99a8a)): ?>
<?php $attributes = $__attributesOriginal7f129feca299ac4c0aa6a1d3bbb99a8a; ?>
<?php unset($__attributesOriginal7f129feca299ac4c0aa6a1d3bbb99a8a); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7f129feca299ac4c0aa6a1d3bbb99a8a)): ?>
<?php $component = $__componentOriginal7f129feca299ac4c0aa6a1d3bbb99a8a; ?>
<?php unset($__componentOriginal7f129feca299ac4c0aa6a1d3bbb99a8a); ?>
<?php endif; ?>
                            <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'admin_phone']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'admin_phone']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b><?php echo e(__('Password')); ?></b></label>
                            <input type="password" name="admin_password" class="form-control"
                                autocomplete="new-password">
                            <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'admin_password']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'admin_password']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b><?php echo e(__('Confirm Password')); ?></b></label>
                            <input type="password" name="admin_password_confirmation" class="form-control">
                        </div>
                    </div>
                </div>

                <!-- DOCUMENTS -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3"><?php echo e(__('Documents')); ?></h5>
                    <div class="row">
                        <div class="mb-3 col-lg-4">
                            <label><b><?php echo e(__('Commercial Registration Certificate')); ?></b></label>
                            <input type="file" name="commercial_registration_certificate" class="form-control">
                            <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'commercial_registration_certificate']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'commercial_registration_certificate']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label>
                                <label><b><?php echo e(__('Cargo Insurance Certificate')); ?></b></label>
                            </label>
                            <input type="file" name="cargo_insurance_certificate" class="form-control">
                            <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'cargo_insurance_certificate']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'cargo_insurance_certificate']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b><?php echo e(__('Tax Certificate')); ?></b></label>
                            <input type="file" name="tax_certificate" class="form-control">
                            <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'tax_certificate']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'tax_certificate']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- SHIPPING OPTIONS -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3"><?php echo e(__('Shipping Options')); ?></h5>

                    <?php $__currentLoopData = $shippingTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="border rounded p-3 mb-3">
                            <input type="hidden" name="shipping_options[<?php echo e($index); ?>][shipping_type_id]"
                                value="<?php echo e($type->id); ?>">

                            <div class="form-check mb-2">
                                <input class="form-check-input shipping-type-toggle" type="checkbox"
                                    name="shipping_options[<?php echo e($index); ?>][enabled]" value="1"
                                    id="shipping_type_<?php echo e($type->id); ?>"
                                    <?php echo e(old("shipping_options.$index.enabled") ? 'checked' : ''); ?>

                                    data-target="#shipping-type-details-<?php echo e($type->id); ?>">
                                <label class="form-check-label fw-bold" for="shipping_type_<?php echo e($type->id); ?>">
                                    <?php echo e($type->name); ?>

                                </label>
                            </div>

                            <div class="ps-3 shipping-type-details" id="shipping-type-details-<?php echo e($type->id); ?>"
                                style="<?php echo e(old("shipping_options.$index.enabled") ? '' : 'display: none;'); ?>">

                                <?php if($type->id !== 1): ?>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox"
                                            name="shipping_options[<?php echo e($index); ?>][has_express_delivery]"
                                            value="1" id="express_delivery_<?php echo e($type->id); ?>"
                                            <?php echo e(old("shipping_options.$index.has_express_delivery") ? 'checked' : ''); ?>>
                                        <label class="form-check-label" for="express_delivery_<?php echo e($type->id); ?>">
                                            <?php echo e(__('Has Express Delivery')); ?>

                                        </label>
                                    </div>
                                <?php endif; ?>

                                <!-- Sizes with Transportation Methods -->
                                <div class="mb-3">
                                    <label class="fw-bold"><?php echo e(__('Sizes & Transportation Methods')); ?></label>
                                    <?php $__currentLoopData = $shippingSizes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sizeIndex => $size): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="border rounded p-3 mb-3 shipping-size-container"
                                            data-shipping-type-index="<?php echo e($index); ?>"
                                            data-size-id="<?php echo e($size->id); ?>">

                                            <!-- Size Checkbox -->
                                            <div class="form-check mb-2">
                                                <input class="form-check-input shipping-size-checkbox" type="checkbox"
                                                    name="shipping_options[<?php echo e($index); ?>][shipping_sizes][]"
                                                    value="<?php echo e($size->id); ?>"
                                                    id="size_<?php echo e($type->id); ?>_<?php echo e($size->id); ?>"
                                                    <?php echo e(in_array($size->id, old("shipping_options.$index.shipping_sizes", [])) ? 'checked' : ''); ?>

                                                    data-target="#size-methods-<?php echo e($type->id); ?>-<?php echo e($size->id); ?>">
                                                <label class="form-check-label fw-bold"
                                                    for="size_<?php echo e($type->id); ?>_<?php echo e($size->id); ?>">
                                                    <?php echo e($size->name); ?>

                                                </label>
                                            </div>

                                            <!-- Transportation Methods for this Size -->
                                            <div class="ps-3 size-transportation-methods"
                                                id="size-methods-<?php echo e($type->id); ?>-<?php echo e($size->id); ?>"
                                                style="<?php echo e(in_array($size->id, old("shipping_options.$index.shipping_sizes", [])) ? '' : 'display: none;'); ?>">
                                                <label
                                                    class="fw-bold small text-muted"><?php echo e(__('Transportation Methods')); ?></label>
                                                <div class="row transportation-methods-container"
                                                    data-shipping-type-index="<?php echo e($index); ?>"
                                                    data-size-id="<?php echo e($size->id); ?>">
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'shipping_options.' . $index . '.size_methods']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('shipping_options.' . $index . '.size_methods')]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                                </div>


                                <!-- LOCATIONS -->
                                <?php if($type->id === 1): ?>
                                    <!-- Cities & Areas -->
                                    <div class="mb-3">
                                        <label class="fw-bold"><?php echo e(__('Cities & Areas')); ?></label>
                                        <div class="shipping-city-area-wrapper" data-index="<?php echo e($index); ?>">
                                            <!-- Dynamically added cities go here -->
                                        </div>

                                        <!-- Display validation error for missing cities -->
                                        <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'shipping_options.' . $index . '.cities']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('shipping_options.' . $index . '.cities')]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>

                                        <!-- Display errors for areas inside cities -->
                                        <?php if(old("shipping_options.$index.cities")): ?>
                                            <?php $__currentLoopData = old("shipping_options.$index.cities"); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cityIndex => $cityData): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'shipping_options.' .
                                                    $index .
                                                    '.cities.' .
                                                    $cityIndex .
                                                    '.areas']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('shipping_options.' .
                                                    $index .
                                                    '.cities.' .
                                                    $cityIndex .
                                                    '.areas')]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endif; ?>

                                        <button type="button"
                                            class="btn btn-sm btn-outline-primary mt-2 add-city-button"
                                            data-index="<?php echo e($index); ?>">
                                            + <?php echo e(__('Add City')); ?>

                                        </button>
                                    </div>
                                <?php elseif($type->id === 2): ?>
                                    <!-- Pickup Cities -->
                                    <div class="mb-3">
                                        <label class="fw-bold"><?php echo e(__('Pickup Cities')); ?></label>
                                        <div class="row">
                                            <?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="col">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox"
                                                            name="shipping_options[<?php echo e($index); ?>][pickup_cities][]"
                                                            value="<?php echo e($city->id); ?>"
                                                            id="pickup_<?php echo e($type->id); ?>_<?php echo e($city->id); ?>"
                                                            <?php echo e(in_array($city->id, old("shipping_options.$index.pickup_cities", [])) ? 'checked' : ''); ?>>
                                                        <label class="form-check-label"
                                                            for="pickup_<?php echo e($type->id); ?>_<?php echo e($city->id); ?>">
                                                            <?php echo e($city->name); ?>

                                                        </label>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                        <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'shipping_options.' . $index . '.pickup_cities']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('shipping_options.' . $index . '.pickup_cities')]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                                    </div>

                                    <!-- Delivery Cities -->
                                    <div class="mb-3">
                                        <label class="fw-bold"><?php echo e(__('Delivery Cities')); ?></label>
                                        <div class="row">
                                            <?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="col">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox"
                                                            name="shipping_options[<?php echo e($index); ?>][delivery_cities][]"
                                                            value="<?php echo e($city->id); ?>"
                                                            id="delivery_<?php echo e($type->id); ?>_<?php echo e($city->id); ?>"
                                                            <?php echo e(in_array($city->id, old("shipping_options.$index.delivery_cities", [])) ? 'checked' : ''); ?>>
                                                        <label class="form-check-label"
                                                            for="delivery_<?php echo e($type->id); ?>_<?php echo e($city->id); ?>">
                                                            <?php echo e($city->name); ?>

                                                        </label>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                        <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'shipping_options.' . $index . '.delivery_cities']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('shipping_options.' . $index . '.delivery_cities')]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                                    </div>
                                <?php elseif($type->id === 3): ?>
                                    <!-- Countries -->
                                    <div class="mb-3">
                                        <label class="fw-bold"><?php echo e(__('Countries')); ?></label>
                                        <div class="row">
                                            <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php if($country->id == 1): ?>
                                                    <?php continue; ?>
                                                <?php endif; ?>
                                                <div class="col-md-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox"
                                                            name="shipping_options[<?php echo e($index); ?>][shipping_countries][]"
                                                            value="<?php echo e($country->id); ?>"
                                                            id="country_<?php echo e($type->id); ?>_<?php echo e($country->id); ?>"
                                                            <?php echo e(in_array($country->id, old("shipping_options.$index.shipping_countries", [])) ? 'checked' : ''); ?>>
                                                        <label class="form-check-label"
                                                            for="country_<?php echo e($type->id); ?>_<?php echo e($country->id); ?>">
                                                            <?php echo e($country->name); ?>

                                                        </label>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                        <?php if (isset($component)) { $__componentOriginalf94ed9c5393ef72725d159fe01139746 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf94ed9c5393ef72725d159fe01139746 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.input-error','data' => ['name' => 'shipping_options.' . $index . '.shipping_countries']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('input-error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute('shipping_options.' . $index . '.shipping_countries')]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $attributes = $__attributesOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__attributesOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf94ed9c5393ef72725d159fe01139746)): ?>
<?php $component = $__componentOriginalf94ed9c5393ef72725d159fe01139746; ?>
<?php unset($__componentOriginalf94ed9c5393ef72725d159fe01139746); ?>
<?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <button type="submit" class="btn btn-outline-primary"><?php echo e(__('Create')); ?></button>

            </div>
        </div>
    </form>

    <?php $__env->startPush('css'); ?>
        <style>
            #shipping-options-list .border {
                background: #f9f9f9;
            }
        </style>
    <?php $__env->stopPush(); ?>

    <?php $__env->startPush('js'); ?>
        <script>
            const allCities = <?php echo json_encode($cities, 15, 512) ?>; // Includes areas
            const oldShippingOptions = <?php echo json_encode(old('shipping_options'), 15, 512) ?>;

            function toggleShippingDetails(checkbox) {
                const targetId = checkbox.dataset.target;
                const details = document.querySelector(targetId);
                if (!details) return;

                const inputs = details.querySelectorAll('input, select, textarea');

                if (checkbox.checked) {
                    details.style.display = '';
                    inputs.forEach(input => input.disabled = false);
                } else {
                    details.style.display = 'none';
                    inputs.forEach(input => {
                        if (input.type === 'checkbox' || input.type === 'radio') {
                            input.checked = false;
                        } else if (input.type !== 'hidden') {
                            input.value = '';
                        }
                        input.disabled = true;
                    });
                }
            }

            document.addEventListener('DOMContentLoaded', function() {
                const form = document.querySelector('form');

                // Initialize toggle for all shipping-type checkboxes
                document.querySelectorAll('.shipping-type-toggle').forEach(function(checkbox) {
                    toggleShippingDetails(checkbox); // Set initial visibility
                    checkbox.addEventListener('change', function() {
                        toggleShippingDetails(this);
                    });
                });

                // Handle dynamic "Add City" buttons
                document.querySelectorAll('.add-city-button').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const shippingIndex = this.dataset.index;
                        const container = this.previousElementSibling;
                        const cityCount = container.querySelectorAll('.city-area-group').length;
                        const newCityIndex = cityCount;

                        const group = document.createElement('div');
                        group.className = 'border rounded p-2 mb-2 city-area-group';

                        const citySelectId = `city_select_${shippingIndex}_${newCityIndex}`;
                        const citySelectName =
                            `shipping_options[${shippingIndex}][cities][${newCityIndex}][id]`;

                        let html = `
                    <div class="mb-2">
                        <label class="fw-bold"><?php echo e(__('City')); ?></label>
                        <select class="form-select city-select" name="${citySelectName}" data-shipping-index="${shippingIndex}" data-city-index="${newCityIndex}" id="${citySelectId}">
                            <option value=""><?php echo e(__('Select City')); ?></option>
                            ${allCities.map(city => `<option value="${city.id}">${city.name.<?php echo e(app()->getLocale()); ?>}</option>`).join('')}
                        </select>
                    </div>
                    <div class="area-checkboxes mt-2" id="area_box_${shippingIndex}_${newCityIndex}">
                        <!-- Area checkboxes will appear here -->
                    </div>
                `;

                        group.innerHTML = html;
                        container.appendChild(group);
                    });
                });

                // When city is selected, load its areas
                document.addEventListener('change', function(e) {
                    if (e.target.classList.contains('city-select')) {
                        const shippingIndex = e.target.dataset.shippingIndex;
                        const cityIndex = e.target.dataset.cityIndex;
                        const selectedCityId = e.target.value;
                        const areaBox = document.getElementById(`area_box_${shippingIndex}_${cityIndex}`);

                        areaBox.innerHTML = '';

                        if (!selectedCityId) return;

                        const city = allCities.find(c => c.id == selectedCityId);
                        if (!city) return;

                        const checkboxes = city.areas.map(area => `
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox"
                            name="shipping_options[${shippingIndex}][cities][${cityIndex}][areas][]"
                            value="${area.id}" id="area_${shippingIndex}_${cityIndex}_${area.id}">
                        <label class="form-check-label" for="area_${shippingIndex}_${cityIndex}_${area.id}">
                            ${area.name.<?php echo e(app()->getLocale()); ?>}
                        </label>
                    </div>
                `).join('');

                        areaBox.innerHTML = checkboxes;
                    }
                });

                // Safety net for form submit: disable all unchecked shipping type inputs
                form.addEventListener('submit', function() {
                    document.querySelectorAll('.shipping-type-toggle').forEach(function(checkbox) {
                        if (!checkbox.checked) {
                            const targetId = checkbox.dataset.target;
                            const details = document.querySelector(targetId);
                            if (!details) return;

                            const inputs = details.querySelectorAll('input, select, textarea');
                            inputs.forEach(input => input.disabled = true);
                        }
                    });
                });

                if (oldShippingOptions) {
                    Object.keys(oldShippingOptions).forEach(index => {
                        const option = oldShippingOptions[index];

                        if (parseInt(option.shipping_type_id) === 1 && option.cities) {
                            const container = document.querySelector(
                                `.shipping-city-area-wrapper[data-index="${index}"]`);
                            if (!container) return;

                            option.cities.forEach((cityObj, cityIndex) => {
                                const group = document.createElement('div');
                                group.className = 'border rounded p-2 mb-2 city-area-group';

                                const citySelectId = `city_select_${index}_${cityIndex}`;
                                const citySelectName =
                                    `shipping_options[${index}][cities][${cityIndex}][id]`;

                                const cityOptions = allCities.map(city => {
                                    const selected = city.id == cityObj.id ? 'selected' : '';
                                    return `<option value="${city.id}" ${selected}>${city.name.<?php echo e(app()->getLocale()); ?>}</option>`;
                                }).join('');

                                let html = `
                            <div class="mb-2">
                                <label class="fw-bold">City</label>
                                <select class="form-select city-select" name="${citySelectName}" data-shipping-index="${index}" data-city-index="${cityIndex}" id="${citySelectId}">
                                    <option value="">Select City</option>
                                    ${cityOptions}
                                </select>
                            </div>
                            <div class="area-checkboxes mt-2" id="area_box_${index}_${cityIndex}">
                                ${(() => {
                                    const selectedCity = allCities.find(c => c.id == cityObj.id);
                                    if (!selectedCity) return '';

                                    return selectedCity.areas.map(area => {
                                        const checked = cityObj.areas && cityObj.areas.includes(area.id.toString()) ? 'checked' : '';
                                        return ` <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                    name="shipping_options[${index}][cities][${cityIndex}][areas][]"
                                                    value="${area.id}" id="area_${index}_${cityIndex}_${area.id}" ${checked}>
                                                <label class="form-check-label" for="area_${index}_${cityIndex}_${area.id}">
                                                    ${area.name.<?php echo e(app()->getLocale()); ?>}
                                                </label>
                                            </div> `;
                                    }).join('');
                                })()}
                            </div>
                        `;

                                group.innerHTML = html;
                                container.appendChild(group);
                            });
                        }
                    });
                }

                // Handle showing/hiding transportation methods for individual shipping sizes
                function updateTransportationMethodsForSize(shippingTypeIndex, sizeId, isChecked) {
                    console.log('updateTransportationMethodsForSize called:', shippingTypeIndex, sizeId, isChecked);

                    // Find the shipping type ID from the hidden input
                    const shippingTypeContainer = document.querySelector(
                        `input[name="shipping_options[${shippingTypeIndex}][shipping_type_id]"]`);
                    const shippingTypeId = shippingTypeContainer ? shippingTypeContainer.value : shippingTypeIndex;

                    console.log('Looking for container with ID:', `size-methods-${shippingTypeId}-${sizeId}`);

                    const methodsContainer = document.querySelector(
                        `#size-methods-${shippingTypeId}-${sizeId}`
                    );

                    if (!methodsContainer) {
                        console.log('Methods container not found for ID:', `size-methods-${shippingTypeId}-${sizeId}`);
                        return;
                    }

                    if (isChecked) {
                        // Show the transportation methods container
                        methodsContainer.style.display = 'block';

                        const transportationContainer = methodsContainer.querySelector(
                            '.transportation-methods-container'
                        );

                        // Show loading spinner
                        transportationContainer.innerHTML = `
                            <div class="d-flex align-items-center text-primary">
                                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                <span>Loading transportation methods...</span>
                            </div>
                        `;

                        // Fetch available transportation methods for this specific size
                        fetch('/api/transportion-methods-by-shipping-sizes', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'Accept': 'application/json',
                                },
                                body: JSON.stringify({
                                    shipping_sizes: [sizeId]
                                })
                            })
                            .then(response => {
                                console.log('Response status:', response.status);
                                console.log('Response headers:', response.headers);
                                if (!response.ok) {
                                    throw new Error(`HTTP error! status: ${response.status}`);
                                }
                                return response.json();
                            })
                            .then(data => {
                                console.log('API response for size', sizeId, ':', data);

                                if (data.data) {
                                    const availableMethods = data.data;
                                    console.log('Available methods for size', sizeId, ':', availableMethods);

                                    // Clear loading spinner
                                    transportationContainer.innerHTML = '';

                                    // Get old values for this size (for validation errors)
                                    const oldMethods = (oldShippingOptions?.[shippingTypeIndex]?.size_methods?.[
                                        sizeId
                                    ]) || [];

                                    // Create transportation method checkboxes
                                    availableMethods.forEach(method => {
                                        const colDiv = document.createElement('div');
                                        colDiv.classList.add('col-md-3', 'mb-2');

                                        const formCheck = document.createElement('div');
                                        formCheck.classList.add('form-check');

                                        const checkbox = document.createElement('input');
                                        checkbox.classList.add('form-check-input');
                                        checkbox.type = 'checkbox';
                                        checkbox.value = method.id;
                                        checkbox.id = `method_${shippingTypeId}_${sizeId}_${method.id}`;
                                        checkbox.name =
                                            `shipping_options[${shippingTypeIndex}][size_methods][${sizeId}][]`;

                                        // Check if method was selected before (old values)
                                        if (oldMethods.includes(method.id.toString())) {
                                            checkbox.checked = true;
                                        }

                                        const label = document.createElement('label');
                                        label.classList.add('form-check-label');
                                        label.htmlFor = checkbox.id;
                                        label.textContent = method.name;

                                        formCheck.appendChild(checkbox);
                                        formCheck.appendChild(label);
                                        colDiv.appendChild(formCheck);
                                        transportationContainer.appendChild(colDiv);
                                    });

                                    if (availableMethods.length === 0) {
                                        transportationContainer.innerHTML =
                                            '<div class="text-muted">No transportation methods available for this size</div>';
                                    }
                                } else {
                                    console.log('API response structure:', data);
                                    transportationContainer.innerHTML =
                                        '<div class="text-danger">Failed to load transportation methods</div>';
                                }
                            })
                            .catch(error => {
                                console.error('Error fetching transportation methods for size', sizeId, ':', error);
                                transportationContainer.innerHTML = `
                                    <div class="text-danger">
                                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                        Error loading transportation methods. Please try again.
                                    </div>
                                `;
                            });

                    } else {
                        // Hide the transportation methods container and uncheck all methods
                        methodsContainer.style.display = 'none';
                        const allCheckboxes = methodsContainer.querySelectorAll('input[type="checkbox"]');
                        allCheckboxes.forEach(cb => {
                            cb.checked = false;
                        });
                    }
                }

                // Add event listeners for shipping size changes
                const shippingSizeCheckboxes = document.querySelectorAll('.shipping-size-checkbox');
                console.log('Found shipping size checkboxes:', shippingSizeCheckboxes.length);

                shippingSizeCheckboxes.forEach(checkbox => {
                    console.log('Adding listener to:', checkbox.name);

                    // Handle initial state for already checked checkboxes
                    if (checkbox.checked) {
                        const nameMatch = checkbox.name.match(/shipping_options\[(\d+)\]/);
                        const sizeId = checkbox.value;
                        const shippingTypeIndex = nameMatch ? nameMatch[1] : null;

                        if (shippingTypeIndex && sizeId) {
                            updateTransportationMethodsForSize(shippingTypeIndex, sizeId, true);
                        }
                    }

                    checkbox.addEventListener('change', function() {
                        console.log('Shipping size checkbox changed:', this.name, this.checked);

                        // Extract shipping type index and size ID
                        const nameMatch = this.name.match(/shipping_options\[(\d+)\]/);
                        const sizeId = this.value;
                        const shippingTypeIndex = nameMatch ? nameMatch[1] : null;

                        if (shippingTypeIndex && sizeId) {
                            console.log('Extracted shipping type index:', shippingTypeIndex, 'Size ID:',
                                sizeId);
                            updateTransportationMethodsForSize(shippingTypeIndex, sizeId, this.checked);
                        }
                    });
                });

            });
        </script>
    <?php $__env->stopPush(); ?>



 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal1f9e5f64f242295036c059d9dc1c375c)): ?>
<?php $attributes = $__attributesOriginal1f9e5f64f242295036c059d9dc1c375c; ?>
<?php unset($__attributesOriginal1f9e5f64f242295036c059d9dc1c375c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal1f9e5f64f242295036c059d9dc1c375c)): ?>
<?php $component = $__componentOriginal1f9e5f64f242295036c059d9dc1c375c; ?>
<?php unset($__componentOriginal1f9e5f64f242295036c059d9dc1c375c); ?>
<?php endif; ?>
<?php /**PATH D:\laravel\packz\resources\views/pages/admin/companies/create.blade.php ENDPATH**/ ?>