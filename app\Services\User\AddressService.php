<?php

namespace App\Services\User;

use App\Repositories\AddressRepository;
use App\Repositories\AreaRepository;
use MatanYadaev\EloquentSpatial\Objects\Point;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class AddressService
{
    public function __construct(
        private readonly AddressRepository $addressRepository,
        private readonly AreaRepository $areaRepository
    ) {}

    public function get($id)
    {
        $address = $this->addressRepository->find($id);

        if (!$address || $address->user_id != auth('user')->id()) {
            throw new NotFoundHttpException('Address not found');
        }

        return $address;
    }

    public function create(array $data)
    {
        $data['user_id'] = auth('user')->id();
        $data['location'] = new Point(request('lat'), request('lng'));

        return $this->addressRepository->create($data);
    }

    public function update($id, array $data)
    {
        $address = $this->addressRepository->find($id);

        if (!$address || $address->user_id != auth('user')->id()) {
            throw new NotFoundHttpException('Address not found');
        }

        $data['location'] = new Point(request('lat'), request('lng'));

        return $this->addressRepository->update($address, $data);
    }

    public function delete($id)
    {
        $address = $this->addressRepository->find($id);

        if (!$address || $address->user_id != auth('user')->id()) {
            throw new NotFoundHttpException('Address not found');
        }

        return $address->delete();
    }
}
