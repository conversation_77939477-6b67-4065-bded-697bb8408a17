<?php

namespace Tests\Feature;

use App\Models\Area;
use App\Models\City;
use App\Models\Country;
use App\Models\ShippingSize;
use App\Models\ShippingType;
use App\Models\TransportionMethod;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class CompanyRegistrationRequestTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->createTestData();
    }

    private function createTestData()
    {
        // Create country
        $country = Country::create(['name' => 'Test Country', 'code' => 'TC']);
        
        // Create city and area
        $city = City::create([
            'country_id' => $country->id,
            'name' => 'Test City',
            'status' => 'active'
        ]);
        
        $area = Area::create([
            'city_id' => $city->id,
            'name' => 'Test Area',
            'status' => 'active'
        ]);

        // Create shipping types
        ShippingType::create(['id' => 1, 'name' => 'immediate']);
        ShippingType::create(['id' => 2, 'name' => 'intercity']);
        ShippingType::create(['id' => 3, 'name' => 'international']);

        // Create shipping sizes
        $small = ShippingSize::create(['name' => 'small']);
        $large = ShippingSize::create(['name' => 'large']);

        // Create transportation methods
        $bike = TransportionMethod::create(['name' => 'bike', 'status' => 'active']);
        $motorcycle = TransportionMethod::create(['name' => 'motorcycle', 'status' => 'active']);
        $lury = TransportionMethod::create(['name' => 'lury', 'status' => 'active']);

        // Associate transportation methods with shipping sizes
        $small->transportationMethods()->attach([$bike->id, $motorcycle->id]);
        $large->transportationMethods()->attach([$motorcycle->id, $lury->id]);
    }

    public function test_company_registration_with_new_size_methods_structure()
    {
        Storage::fake('public');
        
        $small = ShippingSize::where('name', 'small')->first();
        $large = ShippingSize::where('name', 'large')->first();
        $bike = TransportionMethod::where('name', 'bike')->first();
        $motorcycle = TransportionMethod::where('name', 'motorcycle')->first();
        $lury = TransportionMethod::where('name', 'lury')->first();
        $city = City::first();
        $area = Area::first();
        $country = Country::first();

        $data = [
            'name' => 'Test Company',
            'country_code' => $country->code,
            'phone' => '+**********',
            'email' => '<EMAIL>',
            'business_registration_number' => '********',
            'address' => 'Test Address',
            'bank_name' => 'Test Bank',
            'bank_account_owner' => 'Test Owner',
            'bank_account_number' => '**********',
            'iban' => '************************',
            'admin_name' => 'Admin User',
            'admin_email' => '<EMAIL>',
            'admin_country_code' => $country->code,
            'admin_phone' => '+**********',
            'admin_password' => 'Password123!',
            'admin_password_confirmation' => 'Password123!',
            'shipping_options' => [
                [
                    'enabled' => true,
                    'shipping_type_id' => 1, // immediate
                    'shipping_sizes' => [$small->id, $large->id],
                    'size_methods' => [
                        $small->id => [$bike->id, $motorcycle->id],
                        $large->id => [$motorcycle->id, $lury->id]
                    ],
                    'cities' => [
                        [
                            'id' => $city->id,
                            'areas' => [$area->id]
                        ]
                    ]
                ]
            ],
            'commercial_registration_certificate' => UploadedFile::fake()->create('cert.pdf', 100),
            'cargo_insurance_certificate' => UploadedFile::fake()->create('insurance.pdf', 100),
        ];

        $response = $this->postJson('/company/register', $data);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => 'registration request has been sent successfully. please wait for approval'
        ]);

        // Verify company was created
        $this->assertDatabaseHas('companies', [
            'name' => 'Test Company',
            'email' => '<EMAIL>',
            'approval_status' => 'pending'
        ]);

        // Verify company admin was created
        $this->assertDatabaseHas('company_admins', [
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'is_super_admin' => true
        ]);
    }

    public function test_validation_fails_with_old_transportion_methods_structure()
    {
        $small = ShippingSize::where('name', 'small')->first();
        $bike = TransportionMethod::where('name', 'bike')->first();
        $city = City::first();
        $area = Area::first();
        $country = Country::first();

        $data = [
            'name' => 'Test Company',
            'country_code' => $country->code,
            'phone' => '+**********',
            'email' => '<EMAIL>',
            'business_registration_number' => '********',
            'address' => 'Test Address',
            'bank_name' => 'Test Bank',
            'bank_account_owner' => 'Test Owner',
            'bank_account_number' => '**********',
            'admin_name' => 'Admin User',
            'admin_email' => '<EMAIL>',
            'admin_country_code' => $country->code,
            'admin_phone' => '+**********',
            'admin_password' => 'Password123!',
            'admin_password_confirmation' => 'Password123!',
            'shipping_options' => [
                [
                    'enabled' => true,
                    'shipping_type_id' => 1,
                    'shipping_sizes' => [$small->id],
                    // Using old structure - should fail validation
                    'transportion_methods' => [$bike->id],
                    'cities' => [
                        [
                            'id' => $city->id,
                            'areas' => [$area->id]
                        ]
                    ]
                ]
            ],
            'commercial_registration_certificate' => UploadedFile::fake()->create('cert.pdf', 100),
            'cargo_insurance_certificate' => UploadedFile::fake()->create('insurance.pdf', 100),
        ];

        $response = $this->postJson('/company/register', $data);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['shipping_options.0.size_methods']);
    }

    public function test_validation_requires_size_methods_for_each_shipping_size()
    {
        $small = ShippingSize::where('name', 'small')->first();
        $large = ShippingSize::where('name', 'large')->first();
        $bike = TransportionMethod::where('name', 'bike')->first();
        $city = City::first();
        $area = Area::first();
        $country = Country::first();

        $data = [
            'name' => 'Test Company',
            'country_code' => $country->code,
            'phone' => '+**********',
            'email' => '<EMAIL>',
            'business_registration_number' => '********',
            'address' => 'Test Address',
            'bank_name' => 'Test Bank',
            'bank_account_owner' => 'Test Owner',
            'bank_account_number' => '**********',
            'admin_name' => 'Admin User',
            'admin_email' => '<EMAIL>',
            'admin_country_code' => $country->code,
            'admin_phone' => '+**********',
            'admin_password' => 'Password123!',
            'admin_password_confirmation' => 'Password123!',
            'shipping_options' => [
                [
                    'enabled' => true,
                    'shipping_type_id' => 1,
                    'shipping_sizes' => [$small->id, $large->id],
                    'size_methods' => [
                        $small->id => [$bike->id],
                        // Missing methods for large size - should fail validation
                    ],
                    'cities' => [
                        [
                            'id' => $city->id,
                            'areas' => [$area->id]
                        ]
                    ]
                ]
            ],
            'commercial_registration_certificate' => UploadedFile::fake()->create('cert.pdf', 100),
            'cargo_insurance_certificate' => UploadedFile::fake()->create('insurance.pdf', 100),
        ];

        $response = $this->postJson('/company/register', $data);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['shipping_options.0.size_methods.' . $large->id]);
    }
}
