<?php

namespace App\Http\Requests\Admin\ShippingSizeTransportionMethod;

use App\Models\ShippingSize;
use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'shipping_sizes' => ['required', 'array'],
        ];

        $shippingSizes = ShippingSize::pluck('id');

        foreach ($shippingSizes as $id) {
            $rules["shipping_sizes.$id"] = ['required', 'array', 'min:1'];
            $rules["shipping_sizes.$id.*"] = ['required', 'exists:transportion_methods,id'];
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            'shipping_sizes.required' => __('Shipping sizes are required.'),
            'shipping_sizes.*.array' => __(':attribute must have at least one transportation method.'),
            'shipping_sizes.*.min' => __(':attribute must have at least one transportation method.'),
            'shipping_sizes.*.*.exists' => __('Invalid transportation method selected for :attribute.'),
        ];
    }

    public function attributes(): array
    {
        $attributes = [];

        $shippingSizes = ShippingSize::pluck('name', 'id');
        foreach ($shippingSizes as $id => $name) {
            $attributes["shipping_sizes.$id"] = $name;
        }

        return $attributes;
    }
}
