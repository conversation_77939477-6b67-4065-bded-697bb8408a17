<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use MatanYadaev\EloquentSpatial\Objects\Point;
use MatanYadaev\EloquentSpatial\Traits\HasSpatial;

class OrderItem extends Model
{
    use HasSpatial;

    protected $fillable = [
        'order_id',
        'shipment_number',
        'cost',
        'fees',
        'total',
        'dropoff_country_id',
        'dropoff_address_id',
        'dropoff_location',
        'recipient_name',
        'country_code',
        'phone',
        'description',
        'status',
    ];

    protected $casts = [
        'dropoff_location' => Point::class,
        'cost' => 'decimal:2',
        'fees' => 'decimal:2',
    ];

    public function getDropoffCoordinatesAttribute(): array
    {
        $location = $this->dropoff_location ?? $this->dropoffAddress->location ?? null;

        return [
            'lat' => $location?->latitude ?? null,
            'lng' => $location?->longitude ?? null,
        ];
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function dropoffAddress(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'dropoff_address_id')->withTrashed();
    }

    public function media(): MorphMany
    {
        return $this->morphMany(Media::class, 'mediable');
    }

    public function dropoffCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'dropoff_country_id')->withTrashed();
    }
}
