<?php

namespace App\Http\Requests\Company;

use App\Models\Company;
use App\Models\CompanyAdmin;
use App\Rules\ActiveAreas;
use App\Rules\ActiveCities;
use App\Rules\ActiveImmeditateShippingCities;
use App\Rules\ActiveTransportionMethods;
use App\Rules\DistinctArrayValues;
use App\Rules\UniquePhone;
use App\Rules\UniqueShippingOptionCities;
use App\Rules\UniqueShippingOptionCityAreas;
use App\Rules\ValidMedia;
use App\Rules\ValidPassword;
use App\Rules\ValidPhone;
use Illuminate\Foundation\Http\FormRequest;

class RegistrationRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'min:5', 'max:320', 'unique:companies,name'],
            'country_code' => ['required', 'string', 'exists:countries,code'],
            'phone' => ['required', 'string', new ValidPhone($this->country_code), new UniquePhone($this->country_code, Company::class)],
            'email' => ['nullable', 'string', 'email:rfc,dns', 'max:255', 'unique:companies,email'],
            'business_registration_number' => ['required', 'string', 'min:8', 'max:20', 'unique:companies,business_registration_number'],
            'address' => ['nullable', 'string', 'min:5', 'max:100'],
            'bank_name' => ['required', 'string', 'min:5', 'max:100'],
            'bank_account_owner' => ['required', 'string', 'min:5', 'max:100'],
            'bank_account_number' => ['required', 'digits_between:10,20'],
            'iban' => ['nullable', 'string', 'regex:/^[A-Z]{2}\d{2}[A-Z0-9]{11,30}$/i'],
            'shipping_options' => ['required', 'array'],
            'shipping_options.*.shipping_type_id' => ['required', 'exists:shipping_types,id', 'distinct'],
            'shipping_options.*.has_express_delivery' => ['required_unless:shipping_options.*.shipping_type_id,1', 'boolean'],
            'shipping_options.*.shipping_sizes' => ['required', new DistinctArrayValues],
            'shipping_options.*.shipping_sizes.*' => ['required', 'exists:shipping_sizes,id'],
            'shipping_options.*.size_methods' => ['required', 'array'],
            'shipping_options.*.size_methods.*' => ['required', 'array', new DistinctArrayValues, new ActiveTransportionMethods],
            'shipping_options.*.size_methods.*.*' => ['required', 'exists:transportion_methods,id'],
            'shipping_options.*.cities' => ['required_if:shipping_options.*.shipping_type_id,1', 'array', new UniqueShippingOptionCities, new ActiveImmeditateShippingCities],
            'shipping_options.*.cities.*.id' => ['required', 'exists:cities,id'],
            'shipping_options.*.cities.*.areas' => ['required', 'array', new UniqueShippingOptionCityAreas, new ActiveAreas],
            'shipping_options.*.cities.*.areas.*' => ['required', 'exists:areas,id'],
            'shipping_options.*.pickup_cities' => ['required_if:shipping_options.*.shipping_type_id,2', new ActiveCities],
            'shipping_options.*.pickup_cities.*' => ['required', 'exists:cities,id'],
            'shipping_options.*.delivery_cities' => ['required_if:shipping_options.*.shipping_type_id,2', new ActiveCities],
            'shipping_options.*.delivery_cities.*' => ['required', 'exists:cities,id'],
            'shipping_options.*.shipping_countries' => ['required_if:shipping_options.*.shipping_type_id,3', 'array'],
            'shipping_options.*.shipping_countries.*' => ['required', 'exists:countries,id'],
            'admin_name' => ['required', 'string', 'min:4', 'max:30'],
            'admin_email' => ['required', 'string', 'email:rfc,dns', 'max:255', 'unique:company_admins,email'],
            'admin_country_code' => ['required', 'string', 'exists:countries,code'],
            'admin_phone' => ['required', 'string', new ValidPhone($this->admin_country_code), new UniquePhone($this->admin_country_code, CompanyAdmin::class)],
            'admin_password' => ['required', 'string', new ValidPassword, 'confirmed'],
            'logo' => ['nullable', new ValidMedia(['image'])],
            'commercial_registration_certificate' => ['required', new ValidMedia(['image', 'pdf'])],
            'tax_certificate' => ['nullable', new ValidMedia(['image', 'pdf'])],
            'cargo_insurance_certificate' => ['required', new ValidMedia(['image', 'pdf'])],
        ];
    }

    public function messages()
    {
        return [
            'shipping_options.*.shipping_sizes.required' => __('Please select at least one size'),
            'shipping_options.*.size_methods.required' => __('Please select at least one transportation method'),
            'shipping_options.*.cities.required_if' => __('Please select at least one city'),
            'shipping_options.*.pickup_cities.required_if' => __('Please select at least one pickup city'),
            'shipping_options.*.delivery_cities.required_if' => __('Please select at least one delivery city'),
            'shipping_options.*.cities.*.areas.required' => __('Please select at least one area'),
            'shipping_options.*.cities.*.areas.*.required_if' => __('Please select at least one area'),
            'shipping_options.*.shipping_countries.required_if' => __('Please select at least one shipping country'),
        ];
    }

    protected function prepareForValidation()
    {
        if ($this->has('shipping_options')) {
            $filtered = collect($this->input('shipping_options'))
                ->filter(fn($option): bool => ! empty($option['enabled']))
                ->map(function ($option) {
                    // Filter out cities with null or empty 'id'
                    if (isset($option['cities']) && is_array($option['cities'])) {
                        $option['cities'] = collect($option['cities'])
                            ->filter(fn($city): bool => ! empty($city['id']))
                            ->values() // Reindex after filtering
                            ->all();
                    }

                    return $option;
                })
                ->values() // Re-index the main array
                ->all();

            $this->merge([
                'shipping_options' => $filtered,
            ]);
        }
    }
}
