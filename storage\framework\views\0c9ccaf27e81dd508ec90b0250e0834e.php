<?php if(session('success') || session('fail')): ?>
    <?php $__env->startPush('js'); ?>
        <script>
            document.addEventListener("DOMContentLoaded", function() {
                <?php if(session('success')): ?>
                    Swal.fire({
                        toast: true,
                        position: 'top-end',
                        icon: 'success',
                        title: <?php echo json_encode(session('success'), 15, 512) ?>,
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true,
                        background: '#198754', // success green
                        color: '#fff',
                        customClass: {
                            popup: 'custom-toast',
                            timerProgressBar: 'custom-timer-bar'
                        }
                    });
                <?php endif; ?>

                <?php if(session('fail')): ?>
                    Swal.fire({
                        toast: true,
                        position: 'top-end',
                        icon: 'error',
                        title: <?php echo json_encode(session('fail'), 15, 512) ?>,
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true,
                        background: '#DC3545', // danger red
                        color: '#fff',
                        customClass: {
                            popup: 'custom-toast',
                            timerProgressBar: 'custom-timer-bar'
                        }
                    });
                <?php endif; ?>
            });
        </script>
    <?php $__env->stopPush(); ?>
<?php endif; ?>
<?php /**PATH D:\laravel\packz\resources\views/components/session-message.blade.php ENDPATH**/ ?>