<?php

namespace App\Models;

use App\Enum\OrderStatus;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, SoftDeletes;

    protected $fillable = [
        'name',
        'country_code',
        'phone',
        'email',
        'birth_date',
        'gender',
        'status',
        'last_login',
        'push_notifications_enabled',
        'app_locale',
    ];

    protected $casts = [
        'last_login' => 'datetime',
        'push_notifications_enabled' => 'boolean',
    ];

    public function getPhoneAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getEmailAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function image()
    {
        return $this->morphOne(Media::class, 'mediable');
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function completedOrders()
    {
        return $this->orders()->where('status', OrderStatus::DELIVERED->value);
    }

    public function addresses()
    {
        return $this->hasMany(Address::class);
    }
}
