<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['countries', 'country_code', 'phone', 'prefix' => '']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['countries', 'country_code', 'phone', 'prefix' => '']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars, $__key, $__value); ?>

<div class="d-flex align-items-center gap-2">
    <div style="min-width: 100px;">
        <select name="<?php echo e($prefix); ?>country_code" id="<?php echo e($prefix); ?>country_code" class="form-select select2" >
            <?php $__currentLoopData = $countries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <option value="<?php echo e($country->code); ?>" <?php if($country_code==$country->code): echo 'selected'; endif; ?> dir="ltr">
                <?php echo e($country->code); ?>

            </option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </select>
    </div>
    <div class="flex-grow-1">
        <input type="number" name="<?php echo e($prefix); ?>phone" id="phone" class="form-control" value="<?php echo e($phone); ?>" min="0" placeholder="<?php echo e(__('Phone number')); ?>">
    </div>
</div><?php /**PATH D:\laravel\packz\resources\views/components/phone-input.blade.php ENDPATH**/ ?>