<x-layout :title="__('Register Company')">
    <x-session-message />
    <form action="{{ route('admin.companies.store') }}" method="POST" enctype="multipart/form-data">
        @csrf
        <div class="card">
            <div class="card-body">

                <!-- BASIC INFO -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3">{{ __('Basic Info') }}</h5>
                    <div class="row">
                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Company Name') }}</b></label>
                            <input type="text" name="name" class="form-control" value="{{ old('name') }}">
                            <x-input-error name="name" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Business Registration Number') }}</b></label>
                            <input type="text" name="business_registration_number" class="form-control"
                                value="{{ old('business_registration_number') }}">
                            <x-input-error name="business_registration_number" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Email') }}</b></label>
                            <input type="text" name="email" class="form-control" value="{{ old('email') }}"
                                autocomplete="off">
                            <x-input-error name="email" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Phone') }}</b></label>
                            <x-phone-input :countries="$countries" :country_code="old('country_code')" :phone="old('phone')" />
                            <x-input-error name="phone" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Address') }}</b></label>
                            <input type="text" name="address" class="form-control" value="{{ old('address') }}">
                            <x-input-error name="address" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Logo') }}</b></label>
                            <input type="file" name="logo" class="form-control">
                            <x-input-error name="logo" />
                        </div>
                    </div>
                </div>

                <!-- BANK ACCOUNT INFO -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3">{{ __('Bank Account Info') }}</h5>
                    <div class="row">
                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Bank Name') }}</b></label>
                            <input type="text" name="bank_name" class="form-control" value="{{ old('bank_name') }}">
                            <x-input-error name="bank_name" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Bank Account Owner') }}</b></label>
                            <input type="text" name="bank_account_owner" class="form-control"
                                value="{{ old('bank_account_owner') }}">
                            <x-input-error name="bank_account_owner" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Bank Account Number') }}</b></label>
                            <input type="text" name="bank_account_number" class="form-control"
                                value="{{ old('bank_account_number') }}">
                            <x-input-error name="bank_account_number" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('IBAN') }}</b></label>
                            <input type="text" name="iban" class="form-control" value="{{ old('iban') }}">
                            <x-input-error name="iban" />
                        </div>
                    </div>
                </div>

                <!-- ADMIN INFO -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3">{{ __('Admin Info') }}</h5>
                    <div class="row">
                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Admin Name') }}</b></label>
                            <input type="text" name="admin_name" class="form-control"
                                value="{{ old('admin_name') }}">
                            <x-input-error name="admin_name" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Admin Email') }}</b></label>
                            <input type="text" name="admin_email" class="form-control"
                                value="{{ old('admin_email') }}">
                            <x-input-error name="admin_email" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Admin Phone') }}</b></label>
                            <x-phone-input :countries="$countries" :country_code="old('admin_country_code')" :phone="old('admin_phone')" prefix="admin_" />
                            <x-input-error name="admin_phone" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Password') }}</b></label>
                            <input type="password" name="admin_password" class="form-control"
                                autocomplete="new-password">
                            <x-input-error name="admin_password" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Confirm Password') }}</b></label>
                            <input type="password" name="admin_password_confirmation" class="form-control">
                        </div>
                    </div>
                </div>

                <!-- DOCUMENTS -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3">{{ __('Documents') }}</h5>
                    <div class="row">
                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Commercial Registration Certificate') }}</b></label>
                            <input type="file" name="commercial_registration_certificate" class="form-control">
                            <x-input-error name="commercial_registration_certificate" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label>
                                <label><b>{{ __('Cargo Insurance Certificate') }}</b></label>
                            </label>
                            <input type="file" name="cargo_insurance_certificate" class="form-control">
                            <x-input-error name="cargo_insurance_certificate" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label><b>{{ __('Tax Certificate') }}</b></label>
                            <input type="file" name="tax_certificate" class="form-control">
                            <x-input-error name="tax_certificate" />
                        </div>
                    </div>
                </div>

                <!-- SHIPPING OPTIONS -->
                <div class="border rounded p-3 mb-4">
                    <h5 class="mb-3">{{ __('Shipping Options') }}</h5>

                    @foreach ($shippingTypes as $index => $type)
                        <div class="border rounded p-3 mb-3">
                            <input type="hidden" name="shipping_options[{{ $index }}][shipping_type_id]"
                                value="{{ $type->id }}">

                            <div class="form-check mb-2">
                                <input class="form-check-input shipping-type-toggle" type="checkbox"
                                    name="shipping_options[{{ $index }}][enabled]" value="1"
                                    id="shipping_type_{{ $type->id }}"
                                    {{ old("shipping_options.$index.enabled") ? 'checked' : '' }}
                                    data-target="#shipping-type-details-{{ $type->id }}">
                                <label class="form-check-label fw-bold" for="shipping_type_{{ $type->id }}">
                                    {{ $type->name }}
                                </label>
                            </div>

                            <div class="ps-3 shipping-type-details" id="shipping-type-details-{{ $type->id }}"
                                style="{{ old("shipping_options.$index.enabled") ? '' : 'display: none;' }}">

                                @if ($type->id !== 1)
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox"
                                            name="shipping_options[{{ $index }}][has_express_delivery]"
                                            value="1" id="express_delivery_{{ $type->id }}"
                                            {{ old("shipping_options.$index.has_express_delivery") ? 'checked' : '' }}>
                                        <label class="form-check-label" for="express_delivery_{{ $type->id }}">
                                            {{ __('Has Express Delivery') }}
                                        </label>
                                    </div>
                                @endif

                                <!-- Sizes with Transportation Methods -->
                                <div class="mb-3">
                                    <label class="fw-bold">{{ __('Sizes & Transportation Methods') }}</label>
                                    @foreach ($shippingSizes as $sizeIndex => $size)
                                        <div class="border rounded p-3 mb-3 shipping-size-container"
                                            data-shipping-type-index="{{ $index }}"
                                            data-size-id="{{ $size->id }}">

                                            <!-- Size Checkbox -->
                                            <div class="form-check mb-2">
                                                <input class="form-check-input shipping-size-checkbox" type="checkbox"
                                                    name="shipping_options[{{ $index }}][shipping_sizes][]"
                                                    value="{{ $size->id }}"
                                                    id="size_{{ $type->id }}_{{ $size->id }}"
                                                    {{ in_array($size->id, old("shipping_options.$index.shipping_sizes", [])) ? 'checked' : '' }}
                                                    data-target="#size-methods-{{ $type->id }}-{{ $size->id }}">
                                                <label class="form-check-label fw-bold"
                                                    for="size_{{ $type->id }}_{{ $size->id }}">
                                                    {{ $size->name }}
                                                </label>
                                            </div>

                                            <!-- Transportation Methods for this Size -->
                                            <div class="ps-3 size-transportation-methods"
                                                id="size-methods-{{ $type->id }}-{{ $size->id }}"
                                                style="{{ in_array($size->id, old("shipping_options.$index.shipping_sizes", [])) ? '' : 'display: none;' }}">
                                                <label
                                                    class="fw-bold small text-muted">{{ __('Transportation Methods') }}</label>
                                                <div class="row transportation-methods-container"
                                                    data-shipping-type-index="{{ $index }}"
                                                    data-size-id="{{ $size->id }}">
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                    <x-input-error :name="'shipping_options.' . $index . '.size_methods'" />
                                </div>


                                <!-- LOCATIONS -->
                                @if ($type->id === 1)
                                    <!-- Cities & Areas -->
                                    <div class="mb-3">
                                        <label class="fw-bold">{{ __('Cities & Areas') }}</label>
                                        <div class="shipping-city-area-wrapper" data-index="{{ $index }}">
                                            <!-- Dynamically added cities go here -->
                                        </div>

                                        <!-- Display validation error for missing cities -->
                                        <x-input-error :name="'shipping_options.' . $index . '.cities'" />

                                        <!-- Display errors for areas inside cities -->
                                        @if (old("shipping_options.$index.cities"))
                                            @foreach (old("shipping_options.$index.cities") as $cityIndex => $cityData)
                                                <x-input-error :name="'shipping_options.' .
                                                    $index .
                                                    '.cities.' .
                                                    $cityIndex .
                                                    '.areas'" />
                                            @endforeach
                                        @endif

                                        <button type="button"
                                            class="btn btn-sm btn-outline-primary mt-2 add-city-button"
                                            data-index="{{ $index }}">
                                            + {{ __('Add City') }}
                                        </button>
                                    </div>
                                @elseif($type->id === 2)
                                    <!-- Pickup Cities -->
                                    <div class="mb-3">
                                        <label class="fw-bold">{{ __('Pickup Cities') }}</label>
                                        <div class="row">
                                            @foreach ($cities as $city)
                                                <div class="col">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox"
                                                            name="shipping_options[{{ $index }}][pickup_cities][]"
                                                            value="{{ $city->id }}"
                                                            id="pickup_{{ $type->id }}_{{ $city->id }}"
                                                            {{ in_array($city->id, old("shipping_options.$index.pickup_cities", [])) ? 'checked' : '' }}>
                                                        <label class="form-check-label"
                                                            for="pickup_{{ $type->id }}_{{ $city->id }}">
                                                            {{ $city->name }}
                                                        </label>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                        <x-input-error :name="'shipping_options.' . $index . '.pickup_cities'" />
                                    </div>

                                    <!-- Delivery Cities -->
                                    <div class="mb-3">
                                        <label class="fw-bold">{{ __('Delivery Cities') }}</label>
                                        <div class="row">
                                            @foreach ($cities as $city)
                                                <div class="col">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox"
                                                            name="shipping_options[{{ $index }}][delivery_cities][]"
                                                            value="{{ $city->id }}"
                                                            id="delivery_{{ $type->id }}_{{ $city->id }}"
                                                            {{ in_array($city->id, old("shipping_options.$index.delivery_cities", [])) ? 'checked' : '' }}>
                                                        <label class="form-check-label"
                                                            for="delivery_{{ $type->id }}_{{ $city->id }}">
                                                            {{ $city->name }}
                                                        </label>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                        <x-input-error :name="'shipping_options.' . $index . '.delivery_cities'" />
                                    </div>
                                @elseif($type->id === 3)
                                    <!-- Countries -->
                                    <div class="mb-3">
                                        <label class="fw-bold">{{ __('Countries') }}</label>
                                        <div class="row">
                                            @foreach ($countries as $country)
                                                @if ($country->id == 1)
                                                    @continue
                                                @endif
                                                <div class="col-md-2">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox"
                                                            name="shipping_options[{{ $index }}][shipping_countries][]"
                                                            value="{{ $country->id }}"
                                                            id="country_{{ $type->id }}_{{ $country->id }}"
                                                            {{ in_array($country->id, old("shipping_options.$index.shipping_countries", [])) ? 'checked' : '' }}>
                                                        <label class="form-check-label"
                                                            for="country_{{ $type->id }}_{{ $country->id }}">
                                                            {{ $country->name }}
                                                        </label>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                        <x-input-error :name="'shipping_options.' . $index . '.shipping_countries'" />
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>

                <button type="submit" class="btn btn-outline-primary">{{ __('Create') }}</button>

            </div>
        </div>
    </form>

    @push('css')
        <style>
            #shipping-options-list .border {
                background: #f9f9f9;
            }
        </style>
    @endpush

    @push('js')
        <script>
            const allCities = @json($cities); // Includes areas
            const oldShippingOptions = @json(old('shipping_options'));

            function toggleShippingDetails(checkbox) {
                const targetId = checkbox.dataset.target;
                const details = document.querySelector(targetId);
                if (!details) return;

                const inputs = details.querySelectorAll('input, select, textarea');

                if (checkbox.checked) {
                    details.style.display = '';
                    inputs.forEach(input => input.disabled = false);
                } else {
                    details.style.display = 'none';
                    inputs.forEach(input => {
                        if (input.type === 'checkbox' || input.type === 'radio') {
                            input.checked = false;
                        } else if (input.type !== 'hidden') {
                            input.value = '';
                        }
                        input.disabled = true;
                    });
                }
            }

            document.addEventListener('DOMContentLoaded', function() {
                const form = document.querySelector('form');

                // Initialize toggle for all shipping-type checkboxes
                document.querySelectorAll('.shipping-type-toggle').forEach(function(checkbox) {
                    toggleShippingDetails(checkbox); // Set initial visibility
                    checkbox.addEventListener('change', function() {
                        toggleShippingDetails(this);
                    });
                });

                // Handle dynamic "Add City" buttons
                document.querySelectorAll('.add-city-button').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const shippingIndex = this.dataset.index;
                        const container = this.previousElementSibling;
                        const cityCount = container.querySelectorAll('.city-area-group').length;
                        const newCityIndex = cityCount;

                        const group = document.createElement('div');
                        group.className = 'border rounded p-2 mb-2 city-area-group';

                        const citySelectId = `city_select_${shippingIndex}_${newCityIndex}`;
                        const citySelectName =
                            `shipping_options[${shippingIndex}][cities][${newCityIndex}][id]`;

                        let html = `
                    <div class="mb-2">
                        <label class="fw-bold">{{ __('City') }}</label>
                        <select class="form-select city-select" name="${citySelectName}" data-shipping-index="${shippingIndex}" data-city-index="${newCityIndex}" id="${citySelectId}">
                            <option value="">{{ __('Select City') }}</option>
                            ${allCities.map(city => `<option value="${city.id}">${city.name.{{ app()->getLocale() }}}</option>`).join('')}
                        </select>
                    </div>
                    <div class="area-checkboxes mt-2" id="area_box_${shippingIndex}_${newCityIndex}">
                        <!-- Area checkboxes will appear here -->
                    </div>
                `;

                        group.innerHTML = html;
                        container.appendChild(group);
                    });
                });

                // When city is selected, load its areas
                document.addEventListener('change', function(e) {
                    if (e.target.classList.contains('city-select')) {
                        const shippingIndex = e.target.dataset.shippingIndex;
                        const cityIndex = e.target.dataset.cityIndex;
                        const selectedCityId = e.target.value;
                        const areaBox = document.getElementById(`area_box_${shippingIndex}_${cityIndex}`);

                        areaBox.innerHTML = '';

                        if (!selectedCityId) return;

                        const city = allCities.find(c => c.id == selectedCityId);
                        if (!city) return;

                        const checkboxes = city.areas.map(area => `
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox"
                            name="shipping_options[${shippingIndex}][cities][${cityIndex}][areas][]"
                            value="${area.id}" id="area_${shippingIndex}_${cityIndex}_${area.id}">
                        <label class="form-check-label" for="area_${shippingIndex}_${cityIndex}_${area.id}">
                            ${area.name.{{ app()->getLocale() }}}
                        </label>
                    </div>
                `).join('');

                        areaBox.innerHTML = checkboxes;
                    }
                });

                // Safety net for form submit: disable all unchecked shipping type inputs
                form.addEventListener('submit', function() {
                    document.querySelectorAll('.shipping-type-toggle').forEach(function(checkbox) {
                        if (!checkbox.checked) {
                            const targetId = checkbox.dataset.target;
                            const details = document.querySelector(targetId);
                            if (!details) return;

                            const inputs = details.querySelectorAll('input, select, textarea');
                            inputs.forEach(input => input.disabled = true);
                        }
                    });
                });

                if (oldShippingOptions) {
                    Object.keys(oldShippingOptions).forEach(index => {
                        const option = oldShippingOptions[index];

                        if (parseInt(option.shipping_type_id) === 1 && option.cities) {
                            const container = document.querySelector(
                                `.shipping-city-area-wrapper[data-index="${index}"]`);
                            if (!container) return;

                            option.cities.forEach((cityObj, cityIndex) => {
                                const group = document.createElement('div');
                                group.className = 'border rounded p-2 mb-2 city-area-group';

                                const citySelectId = `city_select_${index}_${cityIndex}`;
                                const citySelectName =
                                    `shipping_options[${index}][cities][${cityIndex}][id]`;

                                const cityOptions = allCities.map(city => {
                                    const selected = city.id == cityObj.id ? 'selected' : '';
                                    return `<option value="${city.id}" ${selected}>${city.name.{{ app()->getLocale() }}}</option>`;
                                }).join('');

                                let html = `
                            <div class="mb-2">
                                <label class="fw-bold">City</label>
                                <select class="form-select city-select" name="${citySelectName}" data-shipping-index="${index}" data-city-index="${cityIndex}" id="${citySelectId}">
                                    <option value="">Select City</option>
                                    ${cityOptions}
                                </select>
                            </div>
                            <div class="area-checkboxes mt-2" id="area_box_${index}_${cityIndex}">
                                ${(() => {
                                    const selectedCity = allCities.find(c => c.id == cityObj.id);
                                    if (!selectedCity) return '';

                                    return selectedCity.areas.map(area => {
                                        const checked = cityObj.areas && cityObj.areas.includes(area.id.toString()) ? 'checked' : '';
                                        return ` <div class="form-check">
                                                <input class="form-check-input" type="checkbox"
                                                    name="shipping_options[${index}][cities][${cityIndex}][areas][]"
                                                    value="${area.id}" id="area_${index}_${cityIndex}_${area.id}" ${checked}>
                                                <label class="form-check-label" for="area_${index}_${cityIndex}_${area.id}">
                                                    ${area.name.{{ app()->getLocale() }}}
                                                </label>
                                            </div> `;
                                    }).join('');
                                })()}
                            </div>
                        `;

                                group.innerHTML = html;
                                container.appendChild(group);
                            });
                        }
                    });
                }

                // Handle showing/hiding transportation methods for individual shipping sizes
                function updateTransportationMethodsForSize(shippingTypeIndex, sizeId, isChecked) {
                    console.log('updateTransportationMethodsForSize called:', shippingTypeIndex, sizeId, isChecked);

                    // Find the shipping type ID from the hidden input
                    const shippingTypeContainer = document.querySelector(
                        `input[name="shipping_options[${shippingTypeIndex}][shipping_type_id]"]`);
                    const shippingTypeId = shippingTypeContainer ? shippingTypeContainer.value : shippingTypeIndex;

                    console.log('Looking for container with ID:', `size-methods-${shippingTypeId}-${sizeId}`);

                    const methodsContainer = document.querySelector(
                        `#size-methods-${shippingTypeId}-${sizeId}`
                    );

                    if (!methodsContainer) {
                        console.log('Methods container not found for ID:', `size-methods-${shippingTypeId}-${sizeId}`);
                        return;
                    }

                    if (isChecked) {
                        // Show the transportation methods container
                        methodsContainer.style.display = 'block';

                        const transportationContainer = methodsContainer.querySelector(
                            '.transportation-methods-container'
                        );

                        // Show loading spinner
                        transportationContainer.innerHTML = `
                            <div class="d-flex align-items-center text-primary">
                                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                <span>Loading transportation methods...</span>
                            </div>
                        `;

                        // Fetch available transportation methods for this specific size
                        fetch('/api/transportion-methods-by-shipping-sizes', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'Accept': 'application/json',
                                },
                                body: JSON.stringify({
                                    shipping_sizes: [sizeId]
                                })
                            })
                            .then(response => {
                                console.log('Response status:', response.status);
                                console.log('Response headers:', response.headers);
                                if (!response.ok) {
                                    throw new Error(`HTTP error! status: ${response.status}`);
                                }
                                return response.json();
                            })
                            .then(data => {
                                console.log('API response for size', sizeId, ':', data);

                                if (data.data) {
                                    const availableMethods = data.data;
                                    console.log('Available methods for size', sizeId, ':', availableMethods);

                                    // Clear loading spinner
                                    transportationContainer.innerHTML = '';

                                    // Get old values for this size (for validation errors)
                                    const oldMethods = (oldShippingOptions?.[shippingTypeIndex]?.size_methods?.[
                                        sizeId
                                    ]) || [];

                                    // Create transportation method checkboxes
                                    availableMethods.forEach(method => {
                                        const colDiv = document.createElement('div');
                                        colDiv.classList.add('col-md-3', 'mb-2');

                                        const formCheck = document.createElement('div');
                                        formCheck.classList.add('form-check');

                                        const checkbox = document.createElement('input');
                                        checkbox.classList.add('form-check-input');
                                        checkbox.type = 'checkbox';
                                        checkbox.value = method.id;
                                        checkbox.id = `method_${shippingTypeId}_${sizeId}_${method.id}`;
                                        checkbox.name =
                                            `shipping_options[${shippingTypeIndex}][size_methods][${sizeId}][]`;

                                        // Check if method was selected before (old values)
                                        if (oldMethods.includes(method.id.toString())) {
                                            checkbox.checked = true;
                                        }

                                        const label = document.createElement('label');
                                        label.classList.add('form-check-label');
                                        label.htmlFor = checkbox.id;
                                        label.textContent = method.name;

                                        formCheck.appendChild(checkbox);
                                        formCheck.appendChild(label);
                                        colDiv.appendChild(formCheck);
                                        transportationContainer.appendChild(colDiv);
                                    });

                                    if (availableMethods.length === 0) {
                                        transportationContainer.innerHTML =
                                            '<div class="text-muted">No transportation methods available for this size</div>';
                                    }
                                } else {
                                    console.log('API response structure:', data);
                                    transportationContainer.innerHTML =
                                        '<div class="text-danger">Failed to load transportation methods</div>';
                                }
                            })
                            .catch(error => {
                                console.error('Error fetching transportation methods for size', sizeId, ':', error);
                                transportationContainer.innerHTML = `
                                    <div class="text-danger">
                                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                        Error loading transportation methods. Please try again.
                                    </div>
                                `;
                            });

                    } else {
                        // Hide the transportation methods container and uncheck all methods
                        methodsContainer.style.display = 'none';
                        const allCheckboxes = methodsContainer.querySelectorAll('input[type="checkbox"]');
                        allCheckboxes.forEach(cb => {
                            cb.checked = false;
                        });
                    }
                }

                // Add event listeners for shipping size changes
                const shippingSizeCheckboxes = document.querySelectorAll('.shipping-size-checkbox');
                console.log('Found shipping size checkboxes:', shippingSizeCheckboxes.length);

                shippingSizeCheckboxes.forEach(checkbox => {
                    console.log('Adding listener to:', checkbox.name);

                    // Handle initial state for already checked checkboxes
                    if (checkbox.checked) {
                        const nameMatch = checkbox.name.match(/shipping_options\[(\d+)\]/);
                        const sizeId = checkbox.value;
                        const shippingTypeIndex = nameMatch ? nameMatch[1] : null;

                        if (shippingTypeIndex && sizeId) {
                            updateTransportationMethodsForSize(shippingTypeIndex, sizeId, true);
                        }
                    }

                    checkbox.addEventListener('change', function() {
                        console.log('Shipping size checkbox changed:', this.name, this.checked);

                        // Extract shipping type index and size ID
                        const nameMatch = this.name.match(/shipping_options\[(\d+)\]/);
                        const sizeId = this.value;
                        const shippingTypeIndex = nameMatch ? nameMatch[1] : null;

                        if (shippingTypeIndex && sizeId) {
                            console.log('Extracted shipping type index:', shippingTypeIndex, 'Size ID:',
                                sizeId);
                            updateTransportationMethodsForSize(shippingTypeIndex, sizeId, this.checked);
                        }
                    });
                });

            });
        </script>
    @endpush



</x-layout>
