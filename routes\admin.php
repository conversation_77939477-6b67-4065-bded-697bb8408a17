<?php

use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\AreaController;
use App\Http\Controllers\Admin\Auth\AuthenticationController;
use App\Http\Controllers\Admin\Auth\PasswordResetController;
use App\Http\Controllers\Admin\CityController;
use App\Http\Controllers\Admin\CityCoordinatesController;
use App\Http\Controllers\Admin\CompanyController;
use App\Http\Controllers\Admin\CompanyRegistrationRequestController;
use App\Http\Controllers\Admin\CountryController;
use App\Http\Controllers\Admin\DriverController;
use App\Http\Controllers\Admin\OrderController;
use App\Http\Controllers\Admin\ShippingSizeTransportionMethodController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\VehicleController;
use Illuminate\Support\Facades\Route;

// Auth
Route::middleware('guest:admin')->group(function () {
    Route::get('login', [AuthenticationController::class, 'loginView'])->name('login');
    Route::post('login', [AuthenticationController::class, 'login']);
    Route::get('forgot-password', [PasswordResetController::class, 'forgotPassowrd'])->name('forgot-password');
    Route::post('forgot-password', [PasswordResetController::class, 'sendResetCode']);
    Route::get('confirm-reset-code', [PasswordResetController::class, 'confirmResetCodeView'])->name('confirm-reset-code');
    Route::post('confirm-reset-code', [PasswordResetController::class, 'confirmResetCode']);
    Route::get('reset-password', [PasswordResetController::class, 'resetPasswordView'])->name('reset-password');
    Route::post('reset-password', [PasswordResetController::class, 'resetPassword']);
});

Route::post('logout', [AuthenticationController::class, 'logout'])->name('logout')->middleware('auth:admin');

Route::group(
    ['middleware' => ['auth:admin', 'checkAdminStatus']],
    function () {
        Route::get('/', fn() => view('pages.admin.index'))->name('index');
        Route::resource('countries', CountryController::class);
        Route::resource('cities', CityController::class);
        Route::get('cities/{city}/coordinates', [CityCoordinatesController::class, 'getCoordinates']);
        Route::resource('areas', AreaController::class);
        Route::resource('admins', AdminController::class);
        Route::resource('users', UserController::class);
        Route::resource('drivers', DriverController::class);
        Route::get('drivers/{driver}/assign-vehicles', [DriverController::class, 'assignVehiclesView'])->name('drivers.assign-vehicles');
        Route::post('drivers/{driver}/assign-vehicles', [DriverController::class, 'assignVehicles'])->name('drivers.assign-vehicles');
        Route::post('drivers/{driver}/vehicles/{vehicle}/activation', [DriverController::class, 'activation'])->name('drivers.vehicles.activation');
        Route::resource('companies', CompanyController::class);
        Route::resource('company-registration-requests', CompanyRegistrationRequestController::class)->only('index', 'show');
        Route::post('company-registration-requests/{company}/update-approval-status', [CompanyRegistrationRequestController::class, 'updateApprovalStatus'])->name('company-registration-requests.update-approval-status');
        Route::get('companies/{company}/transportation-methods', [CompanyController::class, 'getAssociatedTransportionMethods'])->name('companies.transportation-methods');
        Route::resource('vehicles', VehicleController::class);
        Route::post('vehicles/{vehicle}/update-approval-status', [VehicleController::class, 'updateApprovalStatus'])->name('vehicles.update-approval-status');
        Route::resource('orders', OrderController::class)->only('index', 'show');
        Route::get('companies/{company}/transportation-methods', [CompanyController::class, 'getAssociatedTransportionMethods'])->name('companies.transportation-methods');
        Route::resource('vehicles', VehicleController::class);
        Route::post('orders/{order}/cancel', [OrderController::class, 'cancel'])->name('orders.cancel');
        Route::resource('shipping-size-transportion-methods', ShippingSizeTransportionMethodController::class)->only(['create', 'store']);
    }
);
