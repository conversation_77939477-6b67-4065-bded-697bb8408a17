<?php $__env->startPush('css'); ?>
    <style>
        #shipping-options-list .border {
            background: #f9f9f9;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('js'); ?>
    <script>
        // Common functions and variables
        const allCities = <?php echo json_encode($cities ?? [], 15, 512) ?>; // Includes areas
        const oldShippingOptions = <?php echo json_encode(old('shipping_options') ?? [], 15, 512) ?>;
        const companyShippingData = <?php echo json_encode($companyShippingData ?? [], 15, 512) ?>;

        console.log('Company Shipping Data:', companyShippingData);

        // Toggle shipping details visibility
        function toggleShippingDetails(checkbox) {
            const targetId = checkbox.dataset.target;
            const details = document.querySelector(targetId);
            if (!details) return;

            const inputs = details.querySelectorAll('input, select, textarea');

            if (checkbox.checked) {
                details.style.display = '';
                inputs.forEach(input => input.disabled = false);
            } else {
                details.style.display = 'none';
                inputs.forEach(input => {
                    if (input.type === 'checkbox' || input.type === 'radio') {
                        input.checked = false;
                    } else if (input.type !== 'hidden') {
                        input.value = '';
                    }
                    input.disabled = true;
                });
            }
        }

        // Get existing methods for a size (for edit form)
        function getExistingMethodsForSize(shippingTypeIndex, sizeId) {
            console.log('getExistingMethodsForSize:', shippingTypeIndex, sizeId, companyShippingData);

            if (!companyShippingData || Object.keys(companyShippingData).length === 0) {
                return [];
            }

            // Find the shipping type by index
            const shippingTypeData = companyShippingData[shippingTypeIndex];
            if (!shippingTypeData) {
                return [];
            }

            // Get methods for this size
            return shippingTypeData.size_methods && shippingTypeData.size_methods[sizeId] ?
                shippingTypeData.size_methods[sizeId] : [];
        }

        // Update transportation methods for a shipping size
        function updateTransportationMethodsForSize(shippingTypeIndex, sizeId, isChecked) {
            console.log('updateTransportationMethodsForSize called:', shippingTypeIndex, sizeId, isChecked);

            // Find the shipping type ID from the hidden input
            const shippingTypeContainer = document.querySelector(
                `input[name="shipping_options[${shippingTypeIndex}][shipping_type_id]"]`);
            const shippingTypeId = shippingTypeContainer ? shippingTypeContainer.value : shippingTypeIndex;

            console.log('Looking for container with ID:', `size-methods-${shippingTypeId}-${sizeId}`);

            const methodsContainer = document.querySelector(
                `#size-methods-${shippingTypeId}-${sizeId}`
            );

            if (!methodsContainer) {
                console.log('Methods container not found for ID:', `size-methods-${shippingTypeId}-${sizeId}`);
                return;
            }

            if (isChecked) {
                // Show the transportation methods container
                methodsContainer.style.display = 'block';

                const transportationContainer = methodsContainer.querySelector(
                    '.transportation-methods-container'
                );

                // Show loading spinner
                transportationContainer.innerHTML = `
                    <div class="d-flex align-items-center text-primary">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        <span>Loading transportation methods...</span>
                    </div>
                `;

                // Fetch available transportation methods for this specific size
                fetch('/api/transportion-methods-by-shipping-sizes', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            shipping_sizes: [sizeId]
                        })
                    })
                    .then(response => {
                        console.log('Response status:', response.status);
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('API response for size', sizeId, ':', data);

                        if (data.data) {
                            const availableMethods = data.data;
                            console.log('Available methods for size', sizeId, ':', availableMethods);

                            // Clear loading spinner
                            transportationContainer.innerHTML = '';

                            // Get old values for this size (for validation errors)
                            const oldMethods = (oldShippingOptions?.[shippingTypeIndex]?.size_methods?.[sizeId]) || [];

                            // Get existing methods for edit form
                            const existingMethods = getExistingMethodsForSize(shippingTypeIndex, sizeId);
                            console.log('Existing methods:', existingMethods);

                            // Create transportation method checkboxes
                            availableMethods.forEach(method => {
                                const colDiv = document.createElement('div');
                                colDiv.classList.add('col-md-3', 'mb-2');

                                const formCheck = document.createElement('div');
                                formCheck.classList.add('form-check');

                                const checkbox = document.createElement('input');
                                checkbox.classList.add('form-check-input');
                                checkbox.type = 'checkbox';
                                checkbox.value = method.id;
                                checkbox.id = `method_${shippingTypeId}_${sizeId}_${method.id}`;
                                checkbox.name =
                                    `shipping_options[${shippingTypeIndex}][size_methods][${sizeId}][]`;

                                // Check if method was selected before (old values or existing data)
                                const methodIdStr = method.id.toString();
                                if (oldMethods.includes(methodIdStr) || existingMethods.includes(methodIdStr)) {
                                    checkbox.checked = true;
                                    console.log('Checking method:', methodIdStr);
                                }

                                const label = document.createElement('label');
                                label.classList.add('form-check-label');
                                label.htmlFor = checkbox.id;
                                label.textContent = method.name;

                                formCheck.appendChild(checkbox);
                                formCheck.appendChild(label);
                                colDiv.appendChild(formCheck);
                                transportationContainer.appendChild(colDiv);
                            });

                            if (availableMethods.length === 0) {
                                transportationContainer.innerHTML =
                                    '<div class="text-muted">No transportation methods available for this size</div>';
                            }
                        } else {
                            console.log('API response structure:', data);
                            transportationContainer.innerHTML =
                                '<div class="text-danger">Failed to load transportation methods</div>';
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching transportation methods for size', sizeId, ':', error);
                        transportationContainer.innerHTML = `
                            <div class="text-danger">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                Error loading transportation methods. Please try again.
                            </div>
                        `;
                    });

            } else {
                // Hide the transportation methods container and uncheck all methods
                methodsContainer.style.display = 'none';
                const allCheckboxes = methodsContainer.querySelectorAll('input[type="checkbox"]');
                allCheckboxes.forEach(cb => {
                    cb.checked = false;
                });
            }
        }

        // Initialize transportation methods for checked shipping sizes
        function initializeCheckedShippingSizes() {
            const checkedSizes = document.querySelectorAll('.shipping-size-checkbox:checked');
            console.log('Initializing transportation methods for', checkedSizes.length, 'checked sizes');

            checkedSizes.forEach((checkbox, index) => {
                const nameMatch = checkbox.name.match(/shipping_options\[(\d+)\]/);
                const sizeId = checkbox.value;
                const shippingTypeIndex = nameMatch ? nameMatch[1] : null;

                if (shippingTypeIndex && sizeId) {
                    // Stagger the API calls to avoid overwhelming the server
                    setTimeout(() => {
                        console.log('Initializing size:', sizeId, 'for type index:', shippingTypeIndex);
                        updateTransportationMethodsForSize(shippingTypeIndex, sizeId, true);
                    }, index * 200); // 200ms delay between each call
                }
            });
        }

        // Add city functionality for immediate shipping
        function addCity(shippingIndex, cityIndex, selectedCityId = null, selectedAreas = []) {
            const container = document.querySelector(`.shipping-city-area-wrapper[data-index="${shippingIndex}"]`);
            if (!container) return;

            const group = document.createElement('div');
            group.className = 'border rounded p-2 mb-2 city-area-group';

            const citySelectId = `city_select_${shippingIndex}_${cityIndex}`;
            const citySelectName = `shipping_options[${shippingIndex}][cities][${cityIndex}][id]`;

            let html = `
                <div class="mb-2">
                    <label class="fw-bold"><?php echo e(__('City')); ?></label>
                    <select class="form-select city-select" name="${citySelectName}" data-shipping-index="${shippingIndex}" data-city-index="${cityIndex}" id="${citySelectId}">
                        <option value=""><?php echo e(__('Select City')); ?></option>
                        ${allCities.map(city => `<option value="${city.id}" ${selectedCityId == city.id ? 'selected' : ''}>${city.name}</option>`).join('')}
                    </select>
                </div>
                <div class="area-checkboxes mt-2" id="area_box_${shippingIndex}_${cityIndex}">
                    ${selectedCityId ? getAreaCheckboxes(shippingIndex, cityIndex, selectedCityId, selectedAreas) : ''}
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger mt-2 remove-city-button">
                    <?php echo e(__('Remove City')); ?>

                </button>
            `;

            group.innerHTML = html;
            container.appendChild(group);

            // Add event listener for remove button
            const removeButton = group.querySelector('.remove-city-button');
            removeButton.addEventListener('click', function() {
                group.remove();
            });
        }

        // Get area checkboxes HTML
        function getAreaCheckboxes(shippingIndex, cityIndex, cityId, selectedAreas = []) {
            const city = allCities.find(c => c.id == cityId);
            if (!city || !city.areas) return '';

            return city.areas.map(area => `
                <div class="form-check">
                    <input class="form-check-input" type="checkbox"
                        name="shipping_options[${shippingIndex}][cities][${cityIndex}][areas][]"
                        value="${area.id}" id="area_${shippingIndex}_${cityIndex}_${area.id}"
                        ${selectedAreas.includes(area.id.toString()) ? 'checked' : ''}>
                    <label class="form-check-label" for="area_${shippingIndex}_${cityIndex}_${area.id}">
                        ${area.name}
                    </label>
                </div>
            `).join('');
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');

            // Initialize toggle for all shipping-type checkboxes
            document.querySelectorAll('.shipping-type-toggle').forEach(function(checkbox) {
                toggleShippingDetails(checkbox); // Set initial visibility
                checkbox.addEventListener('change', function() {
                    toggleShippingDetails(this);
                });
            });

            // Handle dynamic "Add City" buttons
            document.querySelectorAll('.add-city-button').forEach(btn => {
                btn.addEventListener('click', function() {
                    const shippingIndex = this.dataset.index;
                    const container = this.previousElementSibling;
                    const cityCount = container.querySelectorAll('.city-area-group').length;
                    addCity(shippingIndex, cityCount);
                });
            });

            // When city is selected, load its areas
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('city-select')) {
                    const shippingIndex = e.target.dataset.shippingIndex;
                    const cityIndex = e.target.dataset.cityIndex;
                    const selectedCityId = e.target.value;
                    const areaBox = document.getElementById(`area_box_${shippingIndex}_${cityIndex}`);

                    areaBox.innerHTML = '';

                    if (!selectedCityId) return;

                    const city = allCities.find(c => c.id == selectedCityId);
                    if (!city) return;

                    areaBox.innerHTML = getAreaCheckboxes(shippingIndex, cityIndex, selectedCityId);
                }
            });

            // Safety net for form submit: disable all unchecked shipping type inputs
            if (form) {
                form.addEventListener('submit', function() {
                    document.querySelectorAll('.shipping-type-toggle').forEach(function(checkbox) {
                        if (!checkbox.checked) {
                            const targetId = checkbox.dataset.target;
                            const details = document.querySelector(targetId);
                            if (!details) return;

                            const inputs = details.querySelectorAll('input, select, textarea');
                            inputs.forEach(input => input.disabled = true);
                        }
                    });
                });
            }

            // Pre-populate cities for edit page or validation errors
            if (oldShippingOptions) {
                Object.keys(oldShippingOptions).forEach(index => {
                    const option = oldShippingOptions[index];

                    if (parseInt(option.shipping_type_id) === 1 && option.cities) {
                        const container = document.querySelector(
                            `.shipping-city-area-wrapper[data-index="${index}"]`);
                        if (!container) return;

                        option.cities.forEach((cityObj, cityIndex) => {
                            addCity(
                                index,
                                cityIndex,
                                cityObj.id,
                                cityObj.areas || []
                            );
                        });
                    }
                });
            }

            // Add event listeners for shipping size changes
            const shippingSizeCheckboxes = document.querySelectorAll('.shipping-size-checkbox');
            console.log('Found shipping size checkboxes:', shippingSizeCheckboxes.length);

            shippingSizeCheckboxes.forEach(checkbox => {
                console.log('Adding listener to:', checkbox.name);

                // Handle initial state for already checked checkboxes
                if (checkbox.checked) {
                    const nameMatch = checkbox.name.match(/shipping_options\[(\d+)\]/);
                    const sizeId = checkbox.value;
                    const shippingTypeIndex = nameMatch ? nameMatch[1] : null;

                    if (shippingTypeIndex && sizeId) {
                        updateTransportationMethodsForSize(shippingTypeIndex, sizeId, true);
                    }
                }

                checkbox.addEventListener('change', function() {
                    console.log('Shipping size checkbox changed:', this.name, this.checked);

                    // Extract shipping type index and size ID
                    const nameMatch = this.name.match(/shipping_options\[(\d+)\]/);
                    const sizeId = this.value;
                    const shippingTypeIndex = nameMatch ? nameMatch[1] : null;

                    if (shippingTypeIndex && sizeId) {
                        console.log('Extracted shipping type index:', shippingTypeIndex, 'Size ID:',
                            sizeId);
                        updateTransportationMethodsForSize(shippingTypeIndex, sizeId, this.checked);
                    }
                });
            });

            // Initialize transportation methods for already checked shipping sizes (for edit page)
            if (Object.keys(companyShippingData).length > 0) {
                setTimeout(() => {
                    initializeCheckedShippingSizes();
                }, 500); // Wait 500ms for DOM to be fully ready
            }
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\laravel\packz\resources\views/pages/admin/companies/partials/scripts.blade.php ENDPATH**/ ?>